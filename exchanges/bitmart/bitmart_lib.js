const ccxt = require('ccxt');

class BitmartAPI {
    constructor() {
        this.bitmart = new ccxt.bitmart({
            options: {
                defaultType: 'swap',  // Попробуем указать тип по умолчанию как swap
            },
            enableRateLimit: true,
        });
    }

    /**
     * Получает данные о фандинге для всех токенов
     * @returns {Promise<Array>} Массив объектов с данными о фандинге
     */
    async getFundings() {
        let bitmartInfo = [];

        try {
            const markets = await this.bitmart.fetchMarkets();
            const futuresMarkets = markets.filter(market => market.type === 'swap');

            bitmartInfo = futuresMarkets.map(market => ({
                exchange: 'bitmart',
                token: market.symbol,
                fundingRate: market.info.funding_rate * 100,
                nextFundingTime: Number(market.info.funding_time),
            }));
        } catch (error) {
            console.error('Ошибка при получении рынков:', error);
        }

        return bitmartInfo;
    }

    /**
     * Форматирует символ из формата Bitfinex в стандартный формат
     * @param {string} symbol - Символ в формате Bitfinex
     * @returns {string} Отформатированный символ
     */
    formatSymbol(symbol) {
        let formattedSymbol = symbol.startsWith('t') ? symbol.substring(1) : symbol;
        const [base, quote] = formattedSymbol.split(':');
        const cleanBase = base.replace('F0', '');
        const cleanQuote = quote.replace('F0', '');
        const finalBase = cleanBase === 'UST' ? 'USDT' : cleanBase;
        const finalQuote = cleanQuote === 'UST' ? 'USDT' : cleanQuote;
        return `${finalBase}/${finalQuote}:${finalQuote}`;
    }

    /**
     * Получает цены фьючерсов
     * @returns {Promise<Array>} Массив объектов с данными о ценах фьючерсов
     */
    async getFuturesPrices() {
        let pricesInfo = [];

        try {
            const tickers = await this.bitmart.fetchTickers();

            pricesInfo = Object.entries(tickers).map(([symbol, ticker]) => ({
                exchange: 'bitmart',
                symbol: symbol,
                lastPrice: ticker.last,
                dailyChange: ticker.change,
                dailyChangePercent: ticker.percentage,
                volume: ticker.volume,
                high: ticker.high,
                low: ticker.low
            }));

        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов:', error);
        }

        return pricesInfo;
    }

    /**
     * Получает объединенные данные о фандинге и ценах фьючерсов
     * @returns {Promise<Array>} Массив объектов с объединенными данными
     */
    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getFundings(),
                this.getFuturesPrices()
            ]);

            const combinedData = [];
            const tokensMap = new Map();

            fundings.forEach(item => {
                tokensMap.set(item.token, {
                    exchange: 'bitmart',
                    token: item.token,
                    fundingRate: item.fundingRate,
                    nextFundingTimestamp: item.nextFundingTime
                });
            });

            prices.forEach(price => {
                const tokenKey = price.symbol;
                const tokenData = tokensMap.get(tokenKey) || {
                    exchange: 'bitmart',
                    token: tokenKey
                };

                combinedData.push({
                    ...tokenData,
                    lastPrice: price.lastPrice,
                    dailyChange: price.dailyChange,
                    dailyChangePercent: price.dailyChangePercent,
                    volume: price.volume,
                    high: price.high,
                    low: price.low
                });
            });

            console.log(`Получены объединенные данные по ${combinedData.length} токенам с Bitmart`);
            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных:', error);
            return [];
        }
    }
}

module.exports = BitmartAPI;
