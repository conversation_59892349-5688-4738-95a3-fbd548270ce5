const BitmartAPI = require('./bitmart_lib');

async function main() {
    const bitmart = new BitmartAPI();

    try {
        // const fundings = await bitmart.getFundings();
        // console.log('Fundings:', fundings);

        // const formattedSymbol = bitmart.formatSymbol('tBTCUSD');
        // console.log('Formatted Symbol:', formattedSymbol);

        // const futuresPrices = await bitmart.getFuturesPrices();
        // console.log('Futures Prices:', futuresPrices);

        const combinedData = await bitmart.getCombinedData();
        console.log('Combined Data:', combinedData);
    } catch (error) {
        console.error('Error:', error);
    }
}

main();
