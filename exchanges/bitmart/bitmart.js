const ccxt = require('ccxt');

// Инициализация экземпляра биржи
const bitmart = new ccxt.bitmart({
    options: {
        defaultType: 'swap',
    },
    // enableRateLimit: true,
});


async function fetchFuturesMarkets() {

    let bitmartInfo = [] // Массив для хранения информации о фьючерсах на бирже Bitmart для экспорта

    try {
        // Получение всех рынков
        const markets = await bitmart.fetchMarkets();

        // Фильтрация рынков, чтобы оставить только фьючерсы (swap)
        const futuresMarkets = markets.filter(market => market.type === 'swap');

        // console.log(futuresMarkets);

        bitmartInfo = futuresMarkets.map(market => ({
            exchange: 'Bitmart',
            token: market.symbol,
            fundingRate: market.info.funding_rate * 100,
            nextFundingTime: Number(market.info.funding_time),
        }));



    } catch (error) {
        console.error('Ошибка при получении рынков:', error);
    }
    // console.log(bitmartInfo);
    return bitmartInfo;
}

// fetchFuturesMarkets();

async function getFuturesPrices() {
    const tickers = await bitmart.fetchTickers();
    console.log('ФЬючи получены')
    console.log(tickers);
}

getFuturesPrices();

module.exports = {
    fetchFuturesMarkets
};

// биржа - bitmart
// futuresMarkets - массив всех фьючерсов, где каждый элемент это обьект , обьект это токен с информацией о нем
// название токена - [futuresMarkets][(обьект с токеном)].symbol
// ставка фандинга - [futuresMarkets][(обьект с токеном)].info.funding_rate
// время до следующего фандинга - [futuresMarkets][(обьект с токеном)].info.funding_time
