const axios = require('axios');
const ccxt = require('ccxt');

class OkxAPI {
    constructor() {
        this.okx = new ccxt.okx({
            enableRateLimit: true,
            timeout: 30000, // Увеличиваем таймаут до 30 секунд
            options: {
                defaultType: 'swap' // Устанавливаем тип по умолчанию на фьючерсы
            }
        });
        this.baseUrl = 'https://www.okx.com';
    }

    async getAllPairsNames() {
        try {
            const options = {
                method: 'GET',
                url: `${this.baseUrl}/api/v5/public/instruments`,
                headers: {
                    'Content-Type': 'application/json',
                },
                params: {
                    instType: 'SWAP',
                },
            };
            const response = await axios(options);
            
            // Получение всех пар с типом SWAP
            return response.data.data.map(pair => pair.instId);
        } catch (error) {
            console.error('Ошибка при получении списка пар OKX:', error.message);
            return [];
        }
    }

    async getAllFundings() {
        try {
            // Используем тот же способ что и в оригинальном файле _okx.js
            const pairs = await this.getAllPairsNames();
            const result = [];
            const delay = 334; // Интервал для соблюдения лимита (3 запроса в секунду)

            for (let i = 0; i < pairs.length; i++) {
                try {
                    const fundingInfo = await this.okx.fetchFundingRate(pairs[i]);
                    
                    result.push({
                        exchange: 'OKX',
                        token: fundingInfo.symbol,
                        fundingRate: fundingInfo.info.fundingRate * 100,
                        nextFundingTime: fundingInfo.info.nextFundingTime
                    });
                } catch (error) {
                    console.error(`Ошибка при обработке пары ${pairs[i]}:`, error.message);
                }

                // Пауза между запросами
                if (i < pairs.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении фандинга с OKX:', error.message);
            return [];
        }
    }

    async getAllFuturesPrices() {
        try {
            // Используем fetchTickers для получения цен фьючерсов
            const tickers = await this.okx.fetchTickers(undefined, { type: 'swap' });
            const result = [];

            for (const [symbol, ticker] of Object.entries(tickers)) {
                // Фильтруем только фьючерсы (символы содержащие :USDT или подобные)
                if (symbol.includes(':USDT') || symbol.includes(':USD') || ticker.info) {
                    result.push({
                        exchange: 'OKX',
                        token: symbol,
                        lastPrice: ticker.last,
                        markPrice: ticker.markPrice || ticker.last,
                        indexPrice: ticker.indexPrice || ticker.last,
                        dailyChange: ticker.change,
                        dailyChangePercent: ticker.percentage,
                        volume24h: ticker.baseVolume,
                        quoteVolume24h: ticker.quoteVolume,
                        high24h: ticker.high,
                        low24h: ticker.low,
                        bid: ticker.bid,
                        ask: ticker.ask,
                        timestamp: ticker.timestamp,
                        openInterest: ticker.info?.openInterest || null
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов с OKX:', error.message);
            return [];
        }
    }

    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getAllFundings(),
                this.getAllFuturesPrices()
            ]);

            console.log(`Получено данных о фандингах: ${fundings.length}`);
            console.log(`Получено данных о ценах: ${prices.length}`);

            // Создаем Map с данными о ценах по токенам
            const pricesMap = new Map();
            prices.forEach(price => {
                pricesMap.set(price.token, price);
            });

            // Объединяем данные
            const combinedData = [];
            let matchedCount = 0;

            fundings.forEach(funding => {
                const priceData = pricesMap.get(funding.token);
                if (priceData) {
                    combinedData.push({
                        ...funding,
                        ...priceData
                    });
                    matchedCount++;
                } else {
                    // Добавляем данные о фандинге даже если нет цены
                    combinedData.push(funding);
                }
            });

            console.log(`Совпадений по токенам: ${matchedCount} из ${fundings.length}`);
            console.log(`Процент совпадений: ${((matchedCount / fundings.length) * 100).toFixed(1)}%`);

            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных с OKX:', error.message);
            return [];
        }
    }
}

module.exports = { OkxAPI };
