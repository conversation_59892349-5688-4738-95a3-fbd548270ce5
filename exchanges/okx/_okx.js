const axios = require('axios');
const ccxt = require('ccxt');
const CryptoJS = require('crypto-js');

const ccxt_okx = new ccxt.okx({
    enableRateLimit: true,
});

const baseUrl = 'https://www.okx.com';
const fundingEndpoint = '/api/v5/public/funding-rate';


const apiKey = 'YOUR_API_KEY';
const secretKey = 'YOUR_SECRET_KEY';
const passphrase = 'YOUR_PASSPHRASE';

async function getAllPairsNames() {
    const options = {
        method: 'GET',
        url: `${baseUrl}/api/v5/public/instruments`,
        headers: {
            'Content-Type': 'application/json',
        },
        params: {
            instType: 'SWAP',
        },
    };
    const response = await axios(options);

    // Получение всех пар с типом SWAP
    return response.data.data.map(pair => pair.instId);
}



async function getAllFundingRates() {

    const okxInfo = []  // массив с данными о фандинге для экспорта

    const pairs = await getAllPairsNames();
    const fundingData = [];
    const delay = 334; // Интервал для соблюдения лимита (3 запроса в секунду)

    for (let i = 0; i < pairs.length; i++) {
        try {
            // const fundingInfo = await getFundingRate(pairs[i]);  //вариант БЕЗ ccxt (мало инфы о токенах. не юзать)
            const fundingInfo = await ccxt_okx.fetchFundingRate(pairs[i]);  //вариант с ccxt
            fundingData.push(fundingInfo);

            // Добавляем данные в okxInfo в нужном формате
            okxInfo.push({
                exchange: 'okx',
                token: fundingInfo.symbol,
                fundingRate: fundingInfo.info.fundingRate * 100,
                nextFundingTime: fundingInfo.info.nextFundingTime
            });
        } catch (error) {
            console.error(`Ошибка при обработке пары ${pairs[i]}:`, error.message);
        }

        // Пауза между запросами
        if (i < pairs.length - 1) {
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // console.log('okxInfo:', okxInfo);
    console.log('Данные с okx получены');
    return okxInfo;
}

// Запуск программы
// getAllFundingRates()

module.exports = {
    getAllFundingRates
};



