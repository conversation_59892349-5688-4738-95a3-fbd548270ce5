const { BybitAPI } = require('./bybit_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ BYBIT API ===\n');

    const bybit = new BybitAPI();

    try {
        // 1. Тестируем метод getAllFundings()
        console.log('1. Тестирование метода getAllFundings()');
        console.log('=' .repeat(50));

        const fundings = await bybit.getAllFundings();
        console.log(`Получено фандинг рейтов: ${fundings.length}`);

        if (fundings.length > 0) {
            console.log('Первые 3 фандинг рейта:');
            fundings.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}: ${item.fundingRate.toFixed(4)}%`);
                console.log(`   Следующее время фандинга: ${new Date(item.nextFundingTime).toLocaleString()}`);
            });
        }
        console.log();

        // 2. Тестируем метод getAllFuturesPrices()
        console.log('2. Тестирование метода getAllFuturesPrices()');
        console.log('=' .repeat(50));

        const futuresPrices = await bybit.getAllFuturesPrices();
        const futuresArray = Object.values(futuresPrices);
        console.log(`Получено цен фьючерсов: ${futuresArray.length}`);

        if (futuresArray.length > 0) {
            console.log('Первые 3 фьючерса:');
            futuresArray.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.symbol}:`);
                console.log(`   Текущая цена: $${item.currentPrice}`);
                console.log(`   Mark цена: $${item.markPrice}`);
                console.log(`   Изменение за 24ч: ${item.change24h ? item.change24h.toFixed(2) : 'N/A'}%`);
                console.log(`   Объем за 24ч: ${item.volume24h}`);
            });
        }
        console.log();

        // 3. Тестируем метод getCombinedData()
        console.log('3. Тестирование метода getCombinedData()');
        console.log('=' .repeat(50));

        const combinedData = await bybit.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);

        if (combinedData.length > 0) {
            console.log('Первые 3 объединенных записи:');
            combinedData.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}:`);
                console.log(`   Фандинг рейт: ${item.fundingRate.toFixed(4)}%`);
                console.log(`   Текущая цена: $${item.currentPrice || 'N/A'}`);
                console.log(`   Mark цена: $${item.markPrice || 'N/A'}`);
                console.log(`   Изменение за 24ч: ${item.change24h ? item.change24h.toFixed(2) : 'N/A'}%`);
                console.log(`   Следующее время фандинга: ${new Date(item.nextFundingTime).toLocaleString()}`);
            });
        }
        console.log();

        // 4. Статистика и сравнение
        console.log('4. Статистика и сравнение методов');
        console.log('=' .repeat(50));

        console.log(`Всего фандинг рейтов: ${fundings.length}`);
        console.log(`Всего цен фьючерсов: ${futuresArray.length}`);
        console.log(`Всего объединенных данных: ${combinedData.length}`);

        // Найдем токены с самыми высокими и низкими фандинг рейтами
        if (fundings.length > 0) {
            const sortedByFunding = [...fundings].sort((a, b) => b.fundingRate - a.fundingRate);

            console.log('\nТоп-3 токена с самыми высокими фандинг рейтами:');
            sortedByFunding.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}: ${item.fundingRate.toFixed(4)}%`);
            });

            console.log('\nТоп-3 токена с самыми низкими фандинг рейтами:');
            sortedByFunding.slice(-3).reverse().forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}: ${item.fundingRate.toFixed(4)}%`);
            });
        }

        // Найдем токены с самыми большими изменениями цены
        if (futuresArray.length > 0) {
            const withChanges = futuresArray.filter(item => item.change24h !== null);
            if (withChanges.length > 0) {
                const sortedByChange = withChanges.sort((a, b) => Math.abs(b.change24h) - Math.abs(a.change24h));

                console.log('\nТоп-3 токена с самыми большими изменениями цены за 24ч:');
                sortedByChange.slice(0, 3).forEach((item, index) => {
                    console.log(`${index + 1}. ${item.symbol}: ${item.change24h.toFixed(2)}%`);
                });
            }
        }

    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);