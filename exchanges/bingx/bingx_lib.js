const ccxt = require('ccxt');
const axios = require('axios');

class BingxAPI {
    constructor() {
        this.bingx = new ccxt.bingx({
            enableRateLimit: true,
            'options': {
                'defaultType': 'swap'  // или 'future' для некоторых бирж
            }
        });
    }

    async getFundingRates() {
        try {
            const fundingRates = await this.bingx.fetchFundingRates();
            const result = [];

            for (const [symbol, data] of Object.entries(fundingRates)) {
                result.push({
                    exchange: 'BingX',
                    token: symbol,
                    fundingRate: data.fundingRate * 100,
                    nextFundingTime: Number(data.info.nextFundingTime)
                });
            }

            return result;
        } catch (err) {
            console.error('BingX error:', err);
            return [];
        }
    }

    async getFuturesPrices() {
        try {
            // Получаем все тикеры
            const tickers = await this.bingx.fetchTickers();
            const result = [];

            for (const [symbol, data] of Object.entries(tickers)) {
                // Проверяем, что это фьючерсный контракт
                if (this.bingx.markets[symbol] && this.bingx.markets[symbol].type === 'swap') {
                    result.push({
                        symbol: symbol,
                        baseAsset: symbol.split('/')[0],
                        quoteAsset: symbol.split('/')[1],
                        currentPrice: data.last,
                        type: 'Swap'
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении фьючерсов BingX:', error.message);
            return [];
        }
    }

    async getAllBingxInfo() {
        try {
            const fundingRates = await this.getFundingRates();
            const futures = await this.getFuturesPrices();

            const result = [];

            // Объединяем данные фандингов и фьючерсов
            for (const fundingItem of fundingRates) {
                const matchedFuture = futures.find(f => f.symbol === fundingItem.token);

                result.push({
                    ...fundingItem,
                    futurePrice: matchedFuture ? matchedFuture.currentPrice : null,
                    futureInfo: matchedFuture || null
                });
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении информации с BingX:', error.message);
            return [];
        }
    }
}

const bingxAPI = new BingxAPI();

module.exports = {
    bingxAPI,
    BingxAPI
};




