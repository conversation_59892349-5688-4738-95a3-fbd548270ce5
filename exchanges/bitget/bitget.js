const ccxt = require('ccxt');

const bitget = new ccxt.bitget({
    enableRateLimit: true
});

function calculateNextFundingTime() {
    const now = new Date();
    const utcHours = now.getUTCHours();

    // Определяем следующее время фандинга (00:00, 08:00, 16:00 UTC)
    let nextFundingHour;
    if (utcHours < 8) {
        nextFundingHour = 8;
    } else if (utcHours < 16) {
        nextFundingHour = 16;
    } else {
        nextFundingHour = 24; // следующий день 00:00
    }

    // Создаем дату следующего фандинга
    const nextFunding = new Date(now);
    nextFunding.setUTCHours(nextFundingHour, 0, 0, 0);

    // Если следующий фандинг будет в следующем дне (после 16:00)
    if (nextFundingHour === 24) {
        nextFunding.setUTCDate(nextFunding.getUTCDate() + 1);
        nextFunding.setUTCHours(0, 0, 0, 0);
    }

    return nextFunding.getTime(); // возвращаем время в миллисекундах
}

async function getAllFundingRates() {
    const bitgetInfo = [];
    try {
        const fundingRates = await bitget.fetchFundingRates();

        // Получаем время следующего фандинга
        const nextFundingTime = calculateNextFundingTime();

        for (const [symbol, data] of Object.entries(fundingRates)) {
            bitgetInfo.push({
                exchange: 'bitget',
                token: symbol,
                fundingRate: data.fundingRate * 100,
                nextFundingTime: nextFundingTime
            });
        }
    } catch (err) {
        console.error('Bitget error:', err);
    }

    return bitgetInfo;
}


if (require.main === module) {
    getAllFundingRates().then(data => {
        console.log('Bitget funding rates:');
        console.log(JSON.stringify(data, null, 2));
    });
}

module.exports = {
    getAllFundingRates
};
