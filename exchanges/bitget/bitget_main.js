const BitgetAPI = require('./bitget_lib');

async function main() {
    const bitget = new BitgetAPI();

    try {
        const fundings = await bitget.getFundings();
        // console.log('Fundings:', fundings);

        // const formattedSymbol = bitget.formatSymbol('tBTCUSD');
        // console.log('Formatted Symbol:', formattedSymbol);

        // const futuresPrices = await bitget.getFuturesPrices();
        // console.log('Futures Prices:', futuresPrices);

        const combinedData = await bitget.getCombinedData();
        console.log('Данные по фьючам и фандингам Bitget получены')
        console.log(combinedData);
    } catch (error) {
        console.error('Error:', error);
    }
}

main();
