const ccxt = require('ccxt');
const axios = require('axios');

class BitgetAPI {
    constructor() {
        this.bitget = new ccxt.bitget({
            enableRateLimit: true,
            'options': {
                'defaultType': 'swap'
            }
        });
        this.baseUrl = 'https://api-pub.bitfinex.com/v2';
    }

    //получает данные о фандинге для всех токенов
    async getFundings() {
        const bitgetInfo = [];

        try {
            const fundingRates = await this.bitget.fetchFundingRates();

            //получаем время следующего фандинга
            const nextFundingTime = this.calculateNextFundingTime();

            //перебираем все пары и собираем нужную информацию
            for (const [symbol, data] of Object.entries(fundingRates)) {
                bitgetInfo.push({
                    exchange: 'bitget',
                    token: symbol,
                    fundingRate: data.fundingRate * 100, //конвертируем в проценты
                    nextFundingTime: nextFundingTime
                });
            }
        } catch (err) {
            console.error('Bitget error:', err);
        }

        return bitgetInfo;
    }

    /**
     * Форматирует символ из формата Bitfinex в стандартный формат
     * @param {string} symbol - Символ в формате Bitfinex
     * @returns {string} Отформатированный символ
     */
    formatSymbol(symbol) {
        let formattedSymbol = symbol.startsWith('t') ? symbol.substring(1) : symbol;
        const [base, quote] = formattedSymbol.split(':');
        const cleanBase = base.replace('F0', '');
        const cleanQuote = quote.replace('F0', '');
        const finalBase = cleanBase === 'UST' ? 'USDT' : cleanBase;
        const finalQuote = cleanQuote === 'UST' ? 'USDT' : cleanQuote;
        return `${finalBase}/${finalQuote}:${finalQuote}`;
    }

    /**
     * Получает цены всех фьючерсов на Bitfinex
     * @returns {Promise<Array>} Массив объектов с символом и ценой фьючерсов
     */
    async getFuturesPrices() {
        try {
            const response = await axios.get(`${this.baseUrl}/tickers?symbols=ALL`);

            const futuresData = response.data.filter(ticker =>
                ticker[0].includes('F0:') && ticker[0].startsWith('t')
            );

            const result = futuresData.map(ticker => {
                const formattedSymbol = this.formatSymbol(ticker[0]);

                return {
                    symbol: formattedSymbol,
                    lastPrice: ticker[7],
                    dailyChange: ticker[6],
                    dailyChangePercent: ticker[5] * 100,
                    volume: ticker[8],
                    high: ticker[9],
                    low: ticker[10]
                };
            });

            // console.log(`Получено ${result.length} фьючерсов с Bitfinex`);
            return result;
        } catch (error) {
            console.error('Ошибка при получении данных о фьючерсах Bitfinex:', error.message);
            return [];
        }
    }

    /**
     * Получает объединенные данные о фандинге и ценах фьючерсов
     * @returns {Promise<Array>} Массив объектов с объединенными данными
     */
    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getFundings(),
                this.getFuturesPrices()
            ]);

            const combinedData = [];
            const tokensMap = new Map();

            fundings.forEach(item => {
                tokensMap.set(item.token, {
                    exchange: 'bitget',
                    token: item.token,
                    fundingRate: item.fundingRate,
                    nextFundingTime: item.nextFundingTime
                });
            });

            prices.forEach(price => {
                const tokenKey = price.symbol;
                const tokenData = tokensMap.get(tokenKey) || {
                    exchange: 'bitget',
                    token: tokenKey
                };

                combinedData.push({
                    ...tokenData,
                    lastPrice: price.lastPrice,
                    dailyChange: price.dailyChange,
                    dailyChangePercent: price.dailyChangePercent,
                    volume: price.volume,
                    high: price.high,
                    low: price.low
                });
            });

            // console.log(`Получены объединенные данные по ${combinedData.length} токенам с Bitfinex`);
            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных:', error);
            return [];
        }
    }

    /**
     * Вычисляет время следующего фандинга
     * @returns {number} Время следующего фандинга в миллисекундах
     */
    calculateNextFundingTime() {
        const now = new Date();
        const utcHours = now.getUTCHours();

        // Определяем следующее время фандинга (00:00, 08:00, 16:00 UTC)
        let nextFundingHour;
        if (utcHours < 8) {
            nextFundingHour = 8;
        } else if (utcHours < 16) {
            nextFundingHour = 16;
        } else {
            nextFundingHour = 24; // следующий день 00:00
        }

        // Создаем дату следующего фандинга
        const nextFunding = new Date(now);
        nextFunding.setUTCHours(nextFundingHour, 0, 0, 0);

        // Если следующий фандинг будет в следующем дне (после 16:00)
        if (nextFundingHour === 24) {
            nextFunding.setUTCDate(nextFunding.getUTCDate() + 1);
            nextFunding.setUTCHours(0, 0, 0, 0);
        }

        return nextFunding.getTime(); // возвращаем время в миллисекундах
    }
}

module.exports = BitgetAPI;
