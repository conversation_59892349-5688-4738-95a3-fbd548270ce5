const ccxt = require('ccxt');


const kucoinfutures = new ccxt.kucoinfutures({
  'options': {
    'defaultType': 'swap'  // или 'future' для некоторых бирж
  }
});


// фандинг в маркетс лежит (убирать из фандинг рейта два нуля (fundingFeeRate: -0.000544, а на бирже -0,0544 и так со всеми токенами))
async function getAllFundingRates() {

  const kucoinInfo = [];
  try {
    const markets = await kucoinfutures.fetchMarkets();
    // console.log(markets);

    for (const market of markets) {
      kucoinInfo.push({
        exchange: 'kucoin',
        token: market.symbol,
        fundingRate: market.info.fundingFeeRate * 100,
        nextFundingTime: market.info.nextFundingRateTime
      });
    }
  } catch (e) {
    console.error('Ошибка:', e.message);
  }

  // console.log(kucoinInfo);

  return kucoinInfo;
}

// getAllFundingRates();

module.exports = {
  getAllFundingRates
};


// биржа - kucoin
// название токена - markets.[обьект с токеном].symbol
// ставка фандинга - markets.(обьект с токеном).info.fundingFeeRate
// время до следующего фандинга - markets.(обьект с токеном).info.nextFundingRateTime