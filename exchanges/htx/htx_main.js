const { HtxAPI } = require('./htx_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ HTX API ===\n');

    const htx = new HtxAPI();

    try {
        //метод getAllFundings()
        const fundings = await htx.getAllFundings();
        console.log(`Получено фандинг рейтов: ${fundings.length}`);
        // console.log(fundings);

        //метод getAllFuturesPrices()
        const futuresPrices = await htx.getAllFuturesPrices();
        console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);
        // console.log(futuresPrices);

        // 3. Тестируем метод getCombinedData()
        const combinedData = await htx.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);

    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
