const ccxt = require('ccxt');


const htx = new ccxt.htx({
  enableRateLimit: true
});




async function getAllFundingRates() {

  const htxInfo = [];

  try {
    const fundingRates = await htx.fetchFundingRates();
    // console.log(fundingRates);

    for (const [symbol, data] of Object.entries(fundingRates)) {
      htxInfo.push({
        exchange: 'htx',
        token: symbol,
        fundingRate: data.fundingRate * 100,
        nextFundingTime: data.fundingTimestamp
      });
    }



  } catch (e) {
    console.error('Ошибка:', e.message);
  }

  // console.log(htxInfo);

  return htxInfo;
}

// getAllFundingRates();


module.exports = {
  getAllFundingRates
};


// биржа - htx
// название токена - fundingRates.(обьект с токеном).symbol
// ставка фандинга - fundingRates.(обьект с токеном).fundingRate
// время до следующего фандинга - fundingRates.(обьект с токеном).fundingTimestamp


