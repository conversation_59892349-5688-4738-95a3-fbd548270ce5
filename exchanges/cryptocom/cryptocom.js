const axios = require('axios');
const ccxt = require('ccxt');

const cryptocom = new ccxt.cryptocom({
    enableRateLimit: true
});


const BASE_URL = 'https://api.crypto.com/exchange/v1';

async function getAllFundingRates() {
    const cryptocomInfo = [];
    try {
        // Получаем список всех инструментов
        const response = await axios.get(`${BASE_URL}/public/get-instruments`);

        const instruments = response.data.result.data;


        // Фильтруем, оставляя только perpetual свопы
        const perpetualPairs = instruments?.filter(instrument =>
            instrument.inst_type === 'PERPETUAL_SWAP'
        ) || [];


        // Получаем funding rate для каждой пары
        const promises = perpetualPairs.map(async (instrument) => {
            try {
                const fundingResponse = await axios.get(
                    `${BASE_URL}/public/get-valuations?instrument_name=${instrument.symbol}&valuation_type=funding_rate&count=1`
                );

                if (fundingResponse.data?.result?.data?.[0]) {
                    const data = fundingResponse.data.result.data[0];
                    // Преобразуем формат токена из AAVEUSD-PERP в AAVE/USDT:USDT
                    const tokenParts = instrument.symbol.replace('-PERP', '').match(/([A-Za-z0-9]+)(USD)/);
                    if (tokenParts) {
                        return {
                            exchange: 'cryptocom',
                            token: `${tokenParts[1]}/USDT:USDT`, // Заменили USD на USDT
                            fundingRate: parseFloat(data.v) * 100, // конвертируем в проценты
                            nextFundingTime: parseInt(data.t) // используем время из API
                        };
                    }
                }
            } catch (err) {
                console.log(`Error getting funding rate for ${instrument.instrument_name}:`, err.message);
            }
            return null;
        });

        const results = await Promise.all(promises);
        const validResults = results.filter(item => item !== null);

        cryptocomInfo.push(...validResults);
    } catch (err) {
        console.log('Crypto.com error:', err);
        if (err.response) {
            console.log('Response data:', err.response.data);
            console.log('Response status:', err.response.status);
        }
    }

    // console.log(cryptocomInfo);
    console.log('Данные с биржи Crypto.com получены');
    return cryptocomInfo;
}

// getAllFundingRates();


async function getAllFuturesPrices() {
    const tickers = await cryptocom.fetchTickers();
    console.log(tickers);
}

getAllFuturesPrices();


module.exports = {
    getAllFundingRates
};
