const ccxt = require('ccxt');

class LbankAPI {
    constructor() {
        this.lbank = new ccxt.lbank({
            enableRateLimit: true,
            timeout: 30000, // Увеличиваем таймаут до 30 секунд
            options: {
                defaultType: 'swap' // Устанавливаем тип по умолчанию на фьючерсы
            }
        });
    }

    async getAllFundings() {
        try {
            // Используем тот же способ что и в оригинальном файле lbank.js
            const fundingRates = await this.lbank.fetchFundingRates();
            const result = [];

            for (const [symbol, data] of Object.entries(fundingRates)) {
                result.push({
                    exchange: 'LBank',
                    token: symbol,
                    fundingRate: data.fundingRate * 100,
                    nextFundingTime: data.fundingTimestamp
                });
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении фандинга с LBank:', error.message);
            return [];
        }
    }

    async getAllFuturesPrices() {
        try {
            // Используем fetchTickers для получения цен фьючерсов
            const tickers = await this.lbank.fetchTickers(undefined, { type: 'swap' });
            const result = [];

            for (const [symbol, ticker] of Object.entries(tickers)) {
                // Фильтруем только фьючерсы (символы содержащие :USDT или подобные)
                if (symbol.includes(':USDT') || symbol.includes(':USD') || ticker.info) {
                    result.push({
                        exchange: 'LBank',
                        token: symbol,
                        lastPrice: ticker.last,
                        markPrice: ticker.markPrice || ticker.last,
                        indexPrice: ticker.indexPrice || ticker.last,
                        dailyChange: ticker.change,
                        dailyChangePercent: ticker.percentage,
                        volume24h: ticker.baseVolume,
                        quoteVolume24h: ticker.quoteVolume,
                        high24h: ticker.high,
                        low24h: ticker.low,
                        bid: ticker.bid,
                        ask: ticker.ask,
                        timestamp: ticker.timestamp,
                        openInterest: ticker.info?.openInterest || null
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов с LBank:', error.message);
            return [];
        }
    }

    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getAllFundings(),
                this.getAllFuturesPrices()
            ]);

            console.log(`Получено данных о фандингах: ${fundings.length}`);
            console.log(`Получено данных о ценах: ${prices.length}`);

            // Создаем Map с данными о ценах по токенам
            const pricesMap = new Map();
            prices.forEach(price => {
                pricesMap.set(price.token, price);
            });

            // Объединяем данные
            const combinedData = [];
            let matchedCount = 0;

            fundings.forEach(funding => {
                const priceData = pricesMap.get(funding.token);
                if (priceData) {
                    combinedData.push({
                        ...funding,
                        ...priceData
                    });
                    matchedCount++;
                } else {
                    // Добавляем данные о фандинге даже если нет цены
                    combinedData.push(funding);
                }
            });

            console.log(`Совпадений по токенам: ${matchedCount} из ${fundings.length}`);
            console.log(`Процент совпадений: ${((matchedCount / fundings.length) * 100).toFixed(1)}%`);

            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных с LBank:', error.message);
            return [];
        }
    }
}

module.exports = { LbankAPI };
