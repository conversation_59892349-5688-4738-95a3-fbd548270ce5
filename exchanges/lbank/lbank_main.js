const { LbankAPI } = require('./lbank_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ LBANK API ===\n');

    const lbank = new LbankAPI();

    try {
        // 1. Тестируем метод getAllFundings()
        console.log('1. Тестирование метода getAllFundings()');
        console.log('=' .repeat(50));

        const fundings = await lbank.getAllFundings();
        console.log(`Получено фандинг рейтов: ${fundings.length}`);

        if (fundings.length > 0) {
            console.log('Первые 3 фандинг рейта:');
            fundings.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}: ${item.fundingRate.toFixed(4)}%`);
                console.log(`   Следующее время фандинга: ${item.nextFundingTime}`);
            });
        }
        console.log();

        // 2. Тестируем метод getAllFuturesPrices()
        console.log('2. Тестирование метода getAllFuturesPrices()');
        console.log('=' .repeat(50));

        const futuresPrices = await lbank.getAllFuturesPrices();
        console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);

        if (futuresPrices.length > 0) {
            console.log('Первые 3 фьючерса:');
            futuresPrices.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}:`);
                console.log(`   Текущая цена: $${item.lastPrice}`);
                console.log(`   Mark цена: $${item.markPrice}`);
                console.log(`   Изменение за 24ч: ${item.dailyChangePercent ? item.dailyChangePercent.toFixed(2) : 'N/A'}%`);
                console.log(`   Объем за 24ч: ${item.volume24h}`);
            });
        }
        console.log();

        // 3. Тестируем метод getCombinedData()
        console.log('3. Тестирование метода getCombinedData()');
        console.log('=' .repeat(50));

        const combinedData = await lbank.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);

        if (combinedData.length > 0) {
            console.log('Первые 3 объединенных записи:');
            combinedData.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}:`);
                console.log(`   Фандинг рейт: ${item.fundingRate ? item.fundingRate.toFixed(4) : 'N/A'}%`);
                console.log(`   Текущая цена: $${item.lastPrice || 'N/A'}`);
                console.log(`   Изменение за 24ч: ${item.dailyChangePercent ? item.dailyChangePercent.toFixed(2) : 'N/A'}%`);
                console.log(`   Объем за 24ч: ${item.volume24h || 'N/A'}`);
            });
        }
        console.log();

        // 4. Дополнительная статистика
        console.log('4. Дополнительная статистика');
        console.log('=' .repeat(50));

        // Найдем токены с самыми высокими фандинг рейтами
        if (fundings.length > 0) {
            const sortedByFunding = fundings.sort((a, b) => Math.abs(b.fundingRate) - Math.abs(a.fundingRate));
            
            console.log('Топ-3 токена с самыми высокими фандинг рейтами:');
            sortedByFunding.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.token}: ${item.fundingRate.toFixed(4)}%`);
            });
        }

        // Найдем токены с самыми большими изменениями цены
        if (futuresPrices.length > 0) {
            const withChanges = futuresPrices.filter(item => item.dailyChangePercent !== null);
            if (withChanges.length > 0) {
                const sortedByChange = withChanges.sort((a, b) => Math.abs(b.dailyChangePercent) - Math.abs(a.dailyChangePercent));

                console.log('\nТоп-3 токена с самыми большими изменениями цены за 24ч:');
                sortedByChange.slice(0, 3).forEach((item, index) => {
                    console.log(`${index + 1}. ${item.token}: ${item.dailyChangePercent.toFixed(2)}%`);
                });
            }
        }

        // Найдем токены с самым большим объемом торгов
        if (futuresPrices.length > 0) {
            const withVolume = futuresPrices.filter(item => item.volume24h !== null && item.volume24h > 0);
            if (withVolume.length > 0) {
                const sortedByVolume = withVolume.sort((a, b) => b.volume24h - a.volume24h);

                console.log('\nТоп-3 токена с самым большим объемом торгов за 24ч:');
                sortedByVolume.slice(0, 3).forEach((item, index) => {
                    console.log(`${index + 1}. ${item.token}: ${item.volume24h.toLocaleString()}`);
                });
            }
        }

    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
