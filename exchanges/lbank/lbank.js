const ccxt = require('ccxt');

const lbank = new ccxt.lbank({
    options: {
        defaultType: 'future',  // Попробуем указать тип по умолчанию как swap
    },
    // enableRateLimit: true,
});

async function getAllFundingRates() {

    let lbankInfo = [];

    try {
        const fundingRates = await lbank.fetchFundingRates();
        // console.log(fundingRates);

        for (const [symbol, data] of Object.entries(fundingRates)) {
            lbankInfo.push({
                exchange: 'lbank',
                token: symbol,
                fundingRate: data.fundingRate * 100,
                nextFundingTime: data.fundingTimestamp
            });
        }

    } catch (e) {
        console.log(e);
        return []
    }

    // console.log(lbankInfo);

    return lbankInfo;
}


// getAllFundingRates();


module.exports = {
    getAllFundingRates,
};

// биржа - lbank
// название токена - fundingRates.(обьект с токеном).symbol
// ставка фандинга - fundingRates.(обьект с токеном).fundingRate
// время до следующего фандинга - fundingRates.(обьект с токеном).fundingTimestamp