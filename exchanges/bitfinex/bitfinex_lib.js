const ccxt = require('ccxt');
const axios = require('axios');

class BitfinexAPI {
    constructor() {
        this.bitfinex = new ccxt.bitfinex({
            enableRateLimit: true,
            'options': {
                'defaultType': 'swap'
            }
        });
        this.baseUrl = 'https://api-pub.bitfinex.com/v2';
    }

    /**
     * Получает данные о фандинге для всех токенов
     * @returns {Promise<Array>} Массив объектов с данными о фандинге
     */
    async getFundings() {
        const bitfinexInfo = [];

        try {
            const markets = await this.bitfinex.loadMarkets();

            const swapMarkets = Object.entries(markets)
                .filter(([symbol, market]) => market.type === 'swap')
                .reduce((acc, [symbol, market]) => {
                    acc[symbol] = market;
                    return acc;
                }, {});

            const tickerNames = Object.keys(swapMarkets);

            let requestCount = 0;
            for (const symbol of tickerNames) {
                try {
                    if (requestCount >= 88) {
                        console.log('Достигнут лимит запросов, ждем 10 секунд...');
                        await new Promise(resolve => setTimeout(resolve, 5000));
                        requestCount = 0;
                    }

                    const fundingRate = await this.bitfinex.fetchFundingRate(symbol);

                    bitfinexInfo.push({
                        exchange: 'bitfinex',
                        token: symbol,
                        fundingRate: fundingRate.fundingRate * 100,
                        nextFundingTimestamp: fundingRate.nextFundingTimestamp
                    });

                    requestCount++;
                    await new Promise(resolve => setTimeout(resolve, 800));

                } catch (error) {
                    console.error(`Ошибка при получении фандинга для ${symbol}:`, error.message);
                }
            }

            console.log('Данные о фандинге с Bitfinex получены');
            return bitfinexInfo;

        } catch (error) {
            console.error('Произошла ошибка при получении фандингов:', error);
            return [];
        }
    }

    /**
     * Форматирует символ из формата Bitfinex в стандартный формат
     * @param {string} symbol - Символ в формате Bitfinex
     * @returns {string} Отформатированный символ
     */
    formatSymbol(symbol) {
        let formattedSymbol = symbol.startsWith('t') ? symbol.substring(1) : symbol;
        const [base, quote] = formattedSymbol.split(':');
        const cleanBase = base.replace('F0', '');
        const cleanQuote = quote.replace('F0', '');
        const finalBase = cleanBase === 'UST' ? 'USDT' : cleanBase;
        const finalQuote = cleanQuote === 'UST' ? 'USDT' : cleanQuote;
        return `${finalBase}/${finalQuote}:${finalQuote}`;
    }

    /**
     * Получает цены всех фьючерсов на Bitfinex
     * @returns {Promise<Array>} Массив объектов с символом и ценой фьючерсов
     */
    async getFuturesPrices() {
        try {
            const response = await axios.get(`${this.baseUrl}/tickers?symbols=ALL`);

            const futuresData = response.data.filter(ticker =>
                ticker[0].includes('F0:') && ticker[0].startsWith('t')
            );

            const result = futuresData.map(ticker => {
                const formattedSymbol = this.formatSymbol(ticker[0]);

                return {
                    symbol: formattedSymbol,
                    lastPrice: ticker[7],
                    dailyChange: ticker[6],
                    dailyChangePercent: ticker[5] * 100,
                    volume: ticker[8],
                    high: ticker[9],
                    low: ticker[10]
                };
            });

            console.log(`Получено ${result.length} фьючерсов с Bitfinex`);
            return result;
        } catch (error) {
            console.error('Ошибка при получении данных о фьючерсах Bitfinex:', error.message);
            return [];
        }
    }

    /**
     * Получает объединенные данные о фандинге и ценах фьючерсов
     * @returns {Promise<Array>} Массив объектов с объединенными данными
     */
    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getFundings(),
                this.getFuturesPrices()
            ]);

            const combinedData = [];
            const tokensMap = new Map();


            fundings.forEach(item => {
                tokensMap.set(item.token, {
                    exchange: 'bitfinex',
                    token: item.token,
                    fundingRate: item.fundingRate,
                    nextFundingTimestamp: item.nextFundingTimestamp
                });
            });


            prices.forEach(price => {
                const tokenKey = price.symbol;
                const tokenData = tokensMap.get(tokenKey) || {
                    exchange: 'bitfinex',
                    token: tokenKey
                };


                combinedData.push({
                    ...tokenData,
                    lastPrice: price.lastPrice,
                    dailyChange: price.dailyChange,
                    dailyChangePercent: price.dailyChangePercent,
                    volume: price.volume,
                    high: price.high,
                    low: price.low
                });
            });

            console.log(`Получены объединенные данные по ${combinedData.length} токенам с Bitfinex`);
            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных:', error);
            return [];
        }
    }
}

module.exports = BitfinexAPI;
