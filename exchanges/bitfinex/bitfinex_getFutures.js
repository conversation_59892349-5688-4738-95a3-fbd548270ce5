const axios = require('axios');

function formatSymbol(symbol) {
    // Убираем префикс 't' если он есть
    let formattedSymbol = symbol.startsWith('t') ? symbol.substring(1) : symbol;

    // Разделяем на базовую и котировочную валюты
    const [base, quote] = formattedSymbol.split(':');

    // Убираем суффикс 'F0'
    const cleanBase = base.replace('F0', '');
    const cleanQuote = quote.replace('F0', '');

    // Заменяем UST на USDT для совместимости с другими биржами
    const finalBase = cleanBase === 'UST' ? 'USDT' : cleanBase;
    const finalQuote = cleanQuote === 'UST' ? 'USDT' : cleanQuote;

    // Возвращаем в формате 'BTC/USDT:USDT'
    return `${finalBase}/${finalQuote}:${finalQuote}`;
}

/**
 * Получает цены всех фьючерсов на Bitfinex
 * @returns {Promise<Array>} Массив объектов с символом и ценой фьючерсов
 */
async function getAllFuturesPrices() {
    const baseUrl = 'https://api-pub.bitfinex.com/v2';

    try {
        // Запрос всех тикеров
        const response = await axios.get(`${baseUrl}/tickers?symbols=ALL`);

        // Фильтруем только фьючерсы (с форматом tXXXF0:USTF0)
        const futuresData = response.data.filter(ticker =>
            ticker[0].includes('F0:') && ticker[0].startsWith('t')
        );

        // Формируем объекты с нужными данными
        const result = futuresData.map(ticker => {
            // Форматируем символ из формата битфайнекс в наш стандартный формат
            const formattedSymbol = formatSymbol(ticker[0]);

            return {
                symbol: formattedSymbol, // Возвращаем только отформатированный символ
                lastPrice: ticker[7], // Текущая цена фьючерса
                dailyChange: ticker[6],
                dailyChangePercent: ticker[5] * 100, // в процентах
                volume: ticker[8],
                high: ticker[9],
                low: ticker[10]
            };
        });

        console.log(`Получено ${result.length} фьючерсов с Bitfinex`);
        return result;
    } catch (error) {
        console.error('Ошибка при получении данных о фьючерсах Bitfinex:', error.message);
        return [];
    }
}


// Пример использования
getAllFuturesPrices().then(data => console.log(data));

module.exports = {
    getAllFuturesPrices,
    formatSymbol
};

