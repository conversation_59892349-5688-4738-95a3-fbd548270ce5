const BitmexAPI = require('./bitmex_lib');

async function main() {
    const bitmex = new BitmexAPI();

    try {
        // const combinedData = await bitmex.getCombinedData();

        // const withFunding = combinedData.filter(item => item.fundingRate !== undefined) //.slice(0, 5);
        // console.log(withFunding.length);

        // Получаем данные только о фьючерсах
        const futuresData = await bitmex.getFuturesPrices();
        console.log('Получено данных о фьючерсах:', futuresData.length);
        console.log('Первые 3 фьючерса:', futuresData);

    } catch (error) {
        console.error('Error:', error);
        console.error('Данные с биржи BitMex не получены, ошибка:', error);
    }
}

main();
