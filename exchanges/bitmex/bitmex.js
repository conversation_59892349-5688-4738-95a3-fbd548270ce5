const ccxt = require('ccxt');


const bitmex = new ccxt.bitmex({
    enableRateLimit: true
});



async function getAllFundingRates() {

    const bitmexInfo = [];

    try {
        const fundingRates = await bitmex.fetchFundingRates();
        // console.log(fundingRates);

        for (const [symbol, data] of Object.entries(fundingRates)) {
            bitmexInfo.push({
                exchange: 'bitmex',
                token: symbol,
                fundingRate: data.fundingRate * 100,
                nextFundingTime: new Date(data.fundingTimestamp).getTime()
            });
        }



    } catch (e) {
        console.error('Ошибка:', e.message);
    }

    console.log(bitmexInfo);

    return bitmexInfo;
}

// getAllFundingRates();


async function getAllFuturesPrices() {
    const bitmexInfo = [];
    const tickers = await bitmex.fetchTickers();

    // Фильтруем тикеры, где state === "Open" и тип фьючерсы (FFWCSX)
    const filteredTickers = Object.entries(tickers)
        .filter(([key, ticker]) => ticker.info.state === "Open" && ticker.info.typ === "FFWCSX")
        .reduce((acc, [key, ticker]) => {
            acc[key] = ticker;
            return acc;
        }, {});

    console.log('Тикеров с состоянием Open:', Object.keys(filteredTickers).length);
    console.log(filteredTickers);

    return filteredTickers;
}


getAllFuturesPrices();


module.exports = {
    getAllFundingRates
};



// биржа - BitMex
// название токена - fundingRates.(обьект с токеном).symbol
// ставка фандинга - fundingRates.(обьект с токеном).fundingRate
// время до следующего фандинга - fundingRates.(обьект с токеном).fundingTimestamp



