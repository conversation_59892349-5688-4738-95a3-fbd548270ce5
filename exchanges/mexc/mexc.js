const axios = require('axios');
const ccxt = require('ccxt');

const MEXC_API_URL = 'https://contract.mexc.com';

function formatSymbol(symbol) {
  // Преобразование формата BTC_USDT в BTC/USDT:USDT
  const [base, quote] = symbol.split('_');
  return `${base}/${quote}:${quote}`;
}

async function getAllFundingRates() {

  const mexcInfo = [];

  try {
    const fundingRates = await axios.get(`${MEXC_API_URL}/api/v1/contract/funding_rate`);

    // Преобразуем данные, изменяя формат символа
    const formattedData = fundingRates.data.data.map(item => ({
      ...item,
      symbol: formatSymbol(item.symbol)
    }));

    // console.log(formattedData);

    for (const data of formattedData) {
      mexcInfo.push({
        exchange: 'mexc',
        token: data.symbol,
        fundingRate: data.fundingRate * 100,
        nextFundingTime: data.nextSettleTime
      });
    }

  } catch (error) {
    console.error('Ошибка при запросе:', error);
  }

  // console.log(mexcInfo);

  return mexcInfo;
}

// getAllFundingRates();

module.exports = {
  getAllFundingRates,
};

// биржа - mexc
// название токена - fundingRates.(обьект с токеном).symbol
// ставка фандинга - fundingRates.(обьект с токеном).fundingRate
// время до следующего фандинга - fundingRates.(обьект с токеном).nextSettleTime