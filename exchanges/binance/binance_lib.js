const ccxt = require('ccxt');
const axios = require('axios');

class BinanceAPI {
    constructor() {
        this.binance = new ccxt.binance({
            enableRateLimit: true,
        });
    }

    async getFundingRates() {
        try {
            const fundingRates = await this.binance.fetchFundingRates();
            const result = [];

            for (const [symbol, data] of Object.entries(fundingRates)) {
                result.push({
                    exchange: 'Binance',
                    token: symbol,
                    fundingRate: data.fundingRate * 100,
                    nextFundingTime: Number(data.info.nextFundingTime)
                });
            }

            return result;
        } catch (err) {
            console.log(err);
            return [];
        }
    }

    async getFuturesPrices() {
        try {
            const response = await axios.get('https://fapi.binance.com/fapi/v1/exchangeInfo');
            const symbols = response.data.symbols;

            const pricesResponse = await axios.get('https://fapi.binance.com/fapi/v1/ticker/price');
            const prices = pricesResponse.data;

            const symbolsWithPrices = symbols.map(symbol => {
                const priceInfo = prices.find(p => p.symbol === symbol.symbol);

                return {
                    symbol: symbol.symbol,
                    baseAsset: symbol.baseAsset,
                    quoteAsset: symbol.quoteAsset,
                    currentPrice: priceInfo ? priceInfo.price : null,
                    type: 'USDM'
                };
            });

            return symbolsWithPrices;
        } catch (error) {
            console.error('Ошибка при получении USD-M фьючерсов:', error.message);
            return [];
        }
    }

    async getAllBinanceInfo() {
        try {
            const fundingRates = await this.binance.fetchFundingRates();
            const futures = await this.getFuturesPrices();

            const result = [];

            for (const [symbol, data] of Object.entries(fundingRates)) {
                let symbolWithoutSlash;

                if (symbol.endsWith(':USDT')) {
                    symbolWithoutSlash = symbol.replace(/\/USDT:USDT$/, 'USDT');
                } else if (symbol.endsWith('/USDT')) {
                    symbolWithoutSlash = symbol.replace(/\/USDT$/, 'USDT');
                } else {
                    symbolWithoutSlash = symbol.replace(/\//, '');
                }

                const futureInfo = futures.find(f => f.symbol === symbolWithoutSlash);

                result.push({
                    exchange: 'Binance',
                    token: symbol,
                    fundingRate: data.fundingRate * 100,
                    nextFundingTime: Number(data.info.nextFundingTime),
                    futurePrice: futureInfo ? parseFloat(futureInfo.currentPrice) : null,
                    futureInfo: futureInfo || null
                });
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении информации с Binance:', error.message);
            return [];
        }
    }
}

const binanceAPI = new BinanceAPI();

module.exports = {
    binanceAPI,
    BinanceAPI
};




