const { binanceAPI } = require('./binance_lib');

async function getBinanceInfo() {
    try {
        const info = await binanceAPI.getAllBinanceInfo();
        console.log(info);
        console.log('Данные с биржи Binance успешно получены');
    } catch (error) {
        console.error('Ошибка при получении информации с Binance:', error.message);
        return [];
    }
    return info;
}

getBinanceInfo();

module.exports = { getBinanceInfo };
