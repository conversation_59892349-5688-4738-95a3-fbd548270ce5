const ccxt = require('ccxt');
const axios = require("axios");

class AscendexExchange {
    constructor() {
        this.BASE_URL = "https://ascendex.com";
        this.exchange = new ccxt.ascendex({
            enableRateLimit: true,
            'options': {
                'defaultType': 'swap'
            }
        });
    }

    /**
     * Получает все фандинг-рейты с биржи Ascendex
     * @returns {Promise<Array>} Массив объектов с информацией о фандинге
     */
    async getAllFunding() {
        try {
            const fundings = await this.exchange.fetchFundingRates();
            const futuresPrices = await this.getFuturesPrices();

            return Object.entries(fundings).map(([symbol, data]) => {
                const matchedFuture = futuresPrices.find(item => item.symbol === symbol);

                return {
                    exchange: 'Ascendex',
                    token: symbol,
                    fundingRate: parseFloat(data.info.fundingRate) * 100,
                    nextFundingTime: Number(data.info.nextFundingTime),
                    futurePrice: matchedFuture ? parseFloat(matchedFuture.lastPrice) : null
                };
            });
        } catch (error) {
            console.error('Ошибка при получении фандинга:', error);
            return [];
        }
    }

    /**
     * Получает цены на фьючерсы с API Ascendex
     * @returns {Promise<Array>} Массив объектов с ценами фьючерсов
     */
    async getFuturesPrices() {
        try {
            const response = await axios.get(`${this.BASE_URL}/api/pro/v2/futures/pricing-data`);
            const contracts = response.data.data.contracts;

            return contracts.map(contract => ({
                ...contract,
                symbol: contract.symbol.replace(/-PERP/g, '/USDT:USDT')
            }));
        } catch (error) {
            console.error('Ошибка при получении данных с биржи Ascendex (Цены на фьючерсы):', error);
            return [];
        }
    }
}

// Пример использования
// const AscendexExchange = require('./ascendex_lib');
// const ascendex = new AscendexExchange();
// ascendex.getAllFunding().then(console.log);

module.exports = AscendexExchange;