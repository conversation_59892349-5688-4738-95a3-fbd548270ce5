const axios = require('axios');
const ccxt = require('ccxt');
const AscendexExchange = require('./ascendex_lib');

const ascendex = new AscendexExchange();

async function AscendexGetFundingsAndFuturesPrices() {

    const ascendexInfo = [];

    try {
        const fundings = await ascendex.getAllFunding();
        const futuresPrices = await ascendex.getFuturesPrices();


        fundings.forEach(fundingItem => {
            ascendexInfo.push({
                exchange: 'Ascendex',
                token: fundingItem.token,
                fundingRate: fundingItem.fundingRate,
                nextFundingTime: fundingItem.nextFundingTime,
                futurePrice: fundingItem.futurePrice,
                markPrice: null,
                indexPrice: null,
                openInterest: null
            });
        });


        futuresPrices.forEach(futureItem => {
            const existingIndex = ascendexInfo.findIndex(item => item.token === futureItem.symbol);

            if (existingIndex !== -1) {

                ascendexInfo[existingIndex].markPrice = parseFloat(futureItem.markPrice);
                ascendexInfo[existingIndex].indexPrice = parseFloat(futureItem.indexPrice);
                ascendexInfo[existingIndex].openInterest = parseFloat(futureItem.openInterest);
            } else {

                ascendexInfo.push({
                    exchange: 'Ascendex',
                    token: futureItem.symbol,
                    fundingRate: parseFloat(futureItem.fundingRate) * 100,
                    nextFundingTime: Number(futureItem.nextFundingTime),
                    futurePrice: parseFloat(futureItem.lastPrice),
                    markPrice: parseFloat(futureItem.markPrice),
                    indexPrice: parseFloat(futureItem.indexPrice),
                    openInterest: parseFloat(futureItem.openInterest)
                });
            }
        });

        // console.log('Объединенные данные:', ascendexInfo);
        console.log('Данные с биржи Ascendex успешно получены');

    } catch (error) {
        console.error('Ошибка при получении фандинга и цен на фьючерсы:', error);
        return [];
    }

    return ascendexInfo;
}

// getFundingsAndFuturesPrices();

module.exports = { AscendexGetFundingsAndFuturesPrices };
