import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicCommonGetV2PublicAnnoucements(params?: {}): Promise<implicitReturnType>;
    publicCommonGetV2PublicTime(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1NoticeQueryAllNotices(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicTime(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicCurrencies(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicProducts(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicProduct(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketTicker(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketTickers(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketFills(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketCandles(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketDepth(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketSpotVipLevel(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1MarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicLoanCoinInfos(params?: {}): Promise<implicitReturnType>;
    publicSpotGetSpotV1PublicLoanHourInterest(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotPublicCoins(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotPublicSymbols(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketVipFeeRate(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketTickers(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketOrderbook(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketCandles(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketFills(params?: {}): Promise<implicitReturnType>;
    publicSpotGetV2SpotMarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketContracts(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketDepth(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketTicker(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketTickers(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketContractVipLevel(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketFills(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketIndex(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketFundingTime(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketHistoryFundRate(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketCurrentFundRate(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketOpenInterest(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketSymbolLeverage(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketQueryPositionLever(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketOpenLimit(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketHistoryIndexCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketHistoryMarkCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetMixV1MarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketVipFeeRate(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketMergeDepth(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketTicker(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketTickers(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketFills(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketFillsHistory(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketHistoryIndexCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketHistoryMarkCandles(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketOpenInterest(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketFundingTime(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketSymbolPrice(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketHistoryFundRate(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketCurrentFundRate(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketContracts(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketQueryPositionLever(params?: {}): Promise<implicitReturnType>;
    publicMixGetV2MixMarketAccountLongShort(params?: {}): Promise<implicitReturnType>;
    publicMarginGetMarginV1CrossPublicInterestRateAndLimit(params?: {}): Promise<implicitReturnType>;
    publicMarginGetMarginV1IsolatedPublicInterestRateAndLimit(params?: {}): Promise<implicitReturnType>;
    publicMarginGetMarginV1CrossPublicTierData(params?: {}): Promise<implicitReturnType>;
    publicMarginGetMarginV1IsolatedPublicTierData(params?: {}): Promise<implicitReturnType>;
    publicMarginGetMarginV1PublicCurrencies(params?: {}): Promise<implicitReturnType>;
    publicMarginGetV2MarginCurrencies(params?: {}): Promise<implicitReturnType>;
    publicMarginGetV2MarginMarketLongShortRatio(params?: {}): Promise<implicitReturnType>;
    publicEarnGetV2EarnLoanPublicCoinInfos(params?: {}): Promise<implicitReturnType>;
    publicEarnGetV2EarnLoanPublicHourInterest(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1WalletDepositAddress(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1WalletWithdrawalList(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1WalletDepositList(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1AccountGetInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1AccountAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1AccountAssetsLite(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1AccountTransferRecords(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1ConvertCurrencies(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1ConvertConvertRecord(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1LoanOngoingOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1LoanRepayHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1LoanReviseHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1LoanBorrowHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotGetSpotV1LoanDebts(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeOrderInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeUnfilledOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeFills(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeCurrentPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotTradeHistoryPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotAccountInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotAccountSubaccountAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotAccountBills(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotAccountTransferRecords(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2AccountFundingAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2AccountBotAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2AccountAllAccountBalance(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotWalletDepositAddress(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotWalletDepositRecords(params?: {}): Promise<implicitReturnType>;
    privateSpotGetV2SpotWalletWithdrawalRecords(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletTransferV2(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletSubTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletWithdrawalV2(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletWithdrawalInner(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1WalletWithdrawalInnerV2(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1AccountSubAccountSpotAssets(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1AccountBills(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeBatchOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeCancelOrderV2(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeCancelSymbolOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeCancelBatchOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeCancelBatchOrdersV2(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeOrderInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeHistory(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TradeFills(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanPlacePlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanModifyPlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanCancelPlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanCurrentPlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanHistoryPlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1PlanBatchCancelPlan(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1ConvertQuotedPrice(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1ConvertTrade(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1LoanBorrow(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1LoanRepay(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1LoanRevisePledge(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderOrderCurrentList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderOrderHistoryList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderCloseTrackingOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderUpdateTpsl(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderFollowerEndOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceOrderSpotInfoList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceConfigGetTraderSettings(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceConfigGetFollowerSettings(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceUserMyTraders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceConfigSetFollowerConfig(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceUserMyFollowers(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceConfigSetProductCode(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceUserRemoveTrader(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceGetRemovableFollower(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceUserRemoveFollower(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceProfitTotalProfitInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceProfitTotalProfitList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceProfitProfitHisList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceProfitProfitHisDetailList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceProfitWaitProfitDetailList(params?: {}): Promise<implicitReturnType>;
    privateSpotPostSpotV1TraceUserGetTraderInfo(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradePlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeBatchOrders(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeBatchCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeCancelSymbolOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradePlacePlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeModifyPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeCancelPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotTradeBatchCancelPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotWalletTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotWalletSubaccountTransfer(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotWalletWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotWalletCancelWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateSpotPostV2SpotWalletModifyDepositAccount(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1AccountAccount(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1AccountAccounts(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PositionSinglePosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PositionSinglePositionV2(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PositionAllPosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PositionAllPositionV2(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PositionHistoryPosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1AccountAccountBill(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1AccountAccountBusinessBill(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderCurrent(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderMarginCoinCurrent(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderHistory(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderHistoryProductType(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderDetail(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderFills(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1OrderAllFills(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PlanCurrentPlan(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1PlanHistoryPlan(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceCurrentTrack(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceFollowerOrder(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceFollowerHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceHistoryTrack(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceSummary(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceProfitSettleTokenIdGroup(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceProfitDateGroupList(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TradeProfitDateList(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceWaitProfitDateList(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceTraderSymbols(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceTraderList(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceTraderDetail(params?: {}): Promise<implicitReturnType>;
    privateMixGetMixV1TraceQueryTraceConfig(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixAccountAccount(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixAccountAccounts(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixAccountSubAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixAccountOpenCount(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixAccountBill(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixMarketQueryPositionLever(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixPositionSinglePosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixPositionAllPosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixPositionHistoryPosition(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderDetail(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderFills(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderFillHistory(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderOrdersPending(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderOrdersHistory(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderOrdersPlanPending(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixOrderOrdersPlanHistory(params?: {}): Promise<implicitReturnType>;
    privateMixGetV2MixMarketPositionLongShort(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountSubAccountContractAssets(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountOpenCount(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountSetLeverage(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountSetMargin(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountSetMarginMode(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1AccountSetPositionMode(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderBatchOrders(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderCancelBatchOrders(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderModifyOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderCancelSymbolOrders(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderCancelAllOrders(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1OrderCloseAllPositions(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanPlacePlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanModifyPlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanModifyPlanPreset(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanPlaceTPSL(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanPlaceTrailStop(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanPlacePositionsTPSL(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanModifyTPSLPlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanCancelPlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanCancelSymbolPlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1PlanCancelAllPlan(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceCloseTrackOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceModifyTPSL(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceCloseTrackOrderBySymbol(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceSetUpCopySymbols(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceFollowerSetBatchTraceConfig(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceFollowerCloseByTrackingNo(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceFollowerCloseByAll(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceFollowerSetTpsl(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceCancelCopyTrader(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceTraderUpdateConfig(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceMyTraderList(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceMyFollowerList(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceRemoveFollower(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TracePublicGetFollowerConfig(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceReportOrderHistoryList(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceReportOrderCurrentList(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceQueryTraderTpslRatioConfig(params?: {}): Promise<implicitReturnType>;
    privateMixPostMixV1TraceTraderUpdateTpslRatioConfig(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixAccountSetLeverage(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixAccountSetMargin(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixAccountSetMarginMode(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixAccountSetPositionMode(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderClickBackhand(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderBatchPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderModifyOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderBatchCancelOrders(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderClosePositions(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderPlaceTpslOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderPlacePlanOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderModifyTpslOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderModifyPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateMixPostV2MixOrderCancelPlanOrder(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1FeeQuery(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1SubVirtualList(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1SubVirtualApiList(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1TaxSpotRecord(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1TaxFutureRecord(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1TaxMarginRecord(params?: {}): Promise<implicitReturnType>;
    privateUserGetUserV1TaxP2pRecord(params?: {}): Promise<implicitReturnType>;
    privateUserGetV2UserVirtualSubaccountList(params?: {}): Promise<implicitReturnType>;
    privateUserGetV2UserVirtualSubaccountApikeyList(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserV1SubVirtualCreate(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserV1SubVirtualModify(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserV1SubVirtualApiBatchCreate(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserV1SubVirtualApiCreate(params?: {}): Promise<implicitReturnType>;
    privateUserPostUserV1SubVirtualApiModify(params?: {}): Promise<implicitReturnType>;
    privateUserPostV2UserCreateVirtualSubaccount(params?: {}): Promise<implicitReturnType>;
    privateUserPostV2UserModifyVirtualSubaccount(params?: {}): Promise<implicitReturnType>;
    privateUserPostV2UserBatchCreateSubaccountAndApikey(params?: {}): Promise<implicitReturnType>;
    privateUserPostV2UserCreateVirtualSubaccountApikey(params?: {}): Promise<implicitReturnType>;
    privateUserPostV2UserModifyVirtualSubaccountApikey(params?: {}): Promise<implicitReturnType>;
    privateP2pGetP2pV1MerchantMerchantList(params?: {}): Promise<implicitReturnType>;
    privateP2pGetP2pV1MerchantMerchantInfo(params?: {}): Promise<implicitReturnType>;
    privateP2pGetP2pV1MerchantAdvList(params?: {}): Promise<implicitReturnType>;
    privateP2pGetP2pV1MerchantOrderList(params?: {}): Promise<implicitReturnType>;
    privateP2pGetV2P2pMerchantList(params?: {}): Promise<implicitReturnType>;
    privateP2pGetV2P2pMerchantInfo(params?: {}): Promise<implicitReturnType>;
    privateP2pGetV2P2pOrderList(params?: {}): Promise<implicitReturnType>;
    privateP2pGetV2P2pAdvList(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountInfo(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubList(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubEmail(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubSpotAssets(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubFutureAssets(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubaccountTransfer(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubaccountDeposit(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubaccountWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetBrokerV1AccountSubApiList(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerAccountInfo(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerAccountSubaccountList(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerAccountSubaccountEmail(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerAccountSubaccountSpotAssets(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerAccountSubaccountFutureAssets(params?: {}): Promise<implicitReturnType>;
    privateBrokerGetV2BrokerManageSubaccountApikeyList(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubCreate(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubModify(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubModifyEmail(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubAddress(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubAutoTransfer(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubApiCreate(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostBrokerV1AccountSubApiModify(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountModifySubaccountEmail(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountCreateSubaccount(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountModifySubaccount(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountSubaccountAddress(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountSubaccountWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerAccountSetSubaccountAutotransfer(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerManageCreateSubaccountApikey(params?: {}): Promise<implicitReturnType>;
    privateBrokerPostV2BrokerManageModifySubaccountApikey(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossAccountRiskRate(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossAccountMaxTransferOutAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedAccountMaxTransferOutAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedOrderOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedOrderHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedOrderFills(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedLoanList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedRepayList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedInterestList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedLiquidationList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedFinList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossOrderOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossOrderHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossOrderFills(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossLoanList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossRepayList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossInterestList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossLiquidationList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossFinList(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1CrossAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateMarginGetMarginV1IsolatedAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedBorrowHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedRepayHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedInterestHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedLiquidationHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedFinancialRecords(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedAccountRiskRate(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedAccountMaxBorrowableAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedAccountMaxTransferOutAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedInterestRateAndLimit(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedTierData(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginCrossedFills(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedBorrowHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedRepayHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedInterestHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedLiquidationHistory(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedFinancialRecords(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedAccountRiskRate(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedAccountMaxBorrowableAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedAccountMaxTransferOutAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedInterestRateAndLimit(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedTierData(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedOpenOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateMarginGetV2MarginIsolatedFills(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossAccountBorrow(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountBorrow(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossAccountRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountRiskRate(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossAccountMaxBorrowableAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountMaxBorrowableAmount(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountFlashRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedAccountQueryFlashRepayStatus(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossAccountFlashRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossAccountQueryFlashRepayStatus(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedOrderPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedOrderBatchPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedOrderCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1IsolatedOrderBatchCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossOrderPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossOrderBatchPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossOrderCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostMarginV1CrossOrderBatchCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedAccountBorrow(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedAccountRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedAccountFlashRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedAccountQueryFlashRepayStatus(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedBatchPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginCrossedBatchCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedAccountBorrow(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedAccountRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedAccountFlashRepay(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedAccountQueryFlashRepayStatus(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedBatchPlaceOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateMarginPostV2MarginIsolatedBatchCancelOrder(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderOrderCurrentTrack(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderOrderHistoryTrack(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderOrderTotalDetail(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderProfitHistorySummarys(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderProfitHistoryDetails(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderProfitDetails(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderProfitsGroupCoinDate(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderConfigQuerySymbols(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixTraderConfigQueryFollowers(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixFollowerQueryCurrentOrders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixFollowerQueryHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixFollowerQuerySettings(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixFollowerQueryTraders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixFollowerQueryQuantityLimit(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixBrokerQueryTraders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixBrokerQueryHistoryTraces(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopyMixBrokerQueryCurrentTraces(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderProfitSummarys(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderProfitHistoryDetails(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderProfitDetails(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderOrderTotalDetail(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderOrderHistoryTrack(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderOrderCurrentTrack(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderConfigQuerySettings(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotTraderConfigQueryFollowers(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotFollowerQueryTraders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotFollowerQueryTraderSymbols(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotFollowerQuerySettings(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotFollowerQueryHistoryOrders(params?: {}): Promise<implicitReturnType>;
    privateCopyGetV2CopySpotFollowerQueryCurrentOrders(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixTraderOrderModifyTpsl(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixTraderOrderClosePositions(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixTraderConfigSettingSymbols(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixTraderConfigSettingBase(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixTraderConfigRemoveFollower(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixFollowerSettingTpsl(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixFollowerSettings(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixFollowerClosePositions(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopyMixFollowerCancelTrader(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotTraderOrderModifyTpsl(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotTraderOrderCloseTracking(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotTraderConfigSettingSymbols(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotTraderConfigRemoveFollower(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotFollowerStopOrder(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotFollowerSettings(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotFollowerSettingTpsl(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotFollowerOrderCloseTracking(params?: {}): Promise<implicitReturnType>;
    privateCopyPostV2CopySpotFollowerCancelTrader(params?: {}): Promise<implicitReturnType>;
    privateTaxGetV2TaxSpotRecord(params?: {}): Promise<implicitReturnType>;
    privateTaxGetV2TaxFutureRecord(params?: {}): Promise<implicitReturnType>;
    privateTaxGetV2TaxMarginRecord(params?: {}): Promise<implicitReturnType>;
    privateTaxGetV2TaxP2pRecord(params?: {}): Promise<implicitReturnType>;
    privateConvertGetV2ConvertCurrencies(params?: {}): Promise<implicitReturnType>;
    privateConvertGetV2ConvertQuotedPrice(params?: {}): Promise<implicitReturnType>;
    privateConvertGetV2ConvertConvertRecord(params?: {}): Promise<implicitReturnType>;
    privateConvertGetV2ConvertBgbConvertCoinList(params?: {}): Promise<implicitReturnType>;
    privateConvertGetV2ConvertBgbConvertRecords(params?: {}): Promise<implicitReturnType>;
    privateConvertPostV2ConvertTrade(params?: {}): Promise<implicitReturnType>;
    privateConvertPostV2ConvertBgbConvert(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsProduct(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsAccount(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsAssets(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsRecords(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsSubscribeInfo(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsSubscribeResult(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSavingsRedeemResult(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinProduct(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinAccount(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinAssets(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinRecords(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinSubscribeInfo(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnSharkfinSubscribeResult(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanOngoingOrders(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanRepayHistory(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanReviseHistory(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanBorrowHistory(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanDebts(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnLoanReduces(params?: {}): Promise<implicitReturnType>;
    privateEarnGetV2EarnAccountAssets(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnSavingsSubscribe(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnSavingsRedeem(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnSharkfinSubscribe(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnLoanBorrow(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnLoanRepay(params?: {}): Promise<implicitReturnType>;
    privateEarnPostV2EarnLoanRevisePledge(params?: {}): Promise<implicitReturnType>;
    privateCommonGetV2CommonTradeRate(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
