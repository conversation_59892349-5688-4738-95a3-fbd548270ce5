import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    v2GetPlatformStatus(params?: {}): Promise<implicitReturnType>;
    v2GetTickers(params?: {}): Promise<implicitReturnType>;
    v2GetTickerSymbol(params?: {}): Promise<implicitReturnType>;
    v2GetTickersHist(params?: {}): Promise<implicitReturnType>;
    v2GetTradesSymbolHist(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolPrecision(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolP0(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolP1(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolP2(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolP3(params?: {}): Promise<implicitReturnType>;
    v2GetBookSymbolR0(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolSideSection(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolSection(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolLongLast(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolLongHist(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolShortLast(params?: {}): Promise<implicitReturnType>;
    v2GetStats1KeySizeSymbolShortHist(params?: {}): Promise<implicitReturnType>;
    v2GetCandlesTradeTimeframeSymbolSection(params?: {}): Promise<implicitReturnType>;
    v2GetCandlesTradeTimeframeSymbolLast(params?: {}): Promise<implicitReturnType>;
    v2GetCandlesTradeTimeframeSymbolHist(params?: {}): Promise<implicitReturnType>;
    publicGetBookSymbol(params?: {}): Promise<implicitReturnType>;
    publicGetLendbookCurrency(params?: {}): Promise<implicitReturnType>;
    publicGetLendsCurrency(params?: {}): Promise<implicitReturnType>;
    publicGetPubtickerSymbol(params?: {}): Promise<implicitReturnType>;
    publicGetStatsSymbol(params?: {}): Promise<implicitReturnType>;
    publicGetSymbols(params?: {}): Promise<implicitReturnType>;
    publicGetSymbolsDetails(params?: {}): Promise<implicitReturnType>;
    publicGetTickers(params?: {}): Promise<implicitReturnType>;
    publicGetTradesSymbol(params?: {}): Promise<implicitReturnType>;
    privatePostAccountFees(params?: {}): Promise<implicitReturnType>;
    privatePostAccountInfos(params?: {}): Promise<implicitReturnType>;
    privatePostBalances(params?: {}): Promise<implicitReturnType>;
    privatePostBasketManage(params?: {}): Promise<implicitReturnType>;
    privatePostCredits(params?: {}): Promise<implicitReturnType>;
    privatePostDepositNew(params?: {}): Promise<implicitReturnType>;
    privatePostFundingClose(params?: {}): Promise<implicitReturnType>;
    privatePostHistory(params?: {}): Promise<implicitReturnType>;
    privatePostHistoryMovements(params?: {}): Promise<implicitReturnType>;
    privatePostKeyInfo(params?: {}): Promise<implicitReturnType>;
    privatePostMarginInfos(params?: {}): Promise<implicitReturnType>;
    privatePostMytrades(params?: {}): Promise<implicitReturnType>;
    privatePostMytradesFunding(params?: {}): Promise<implicitReturnType>;
    privatePostOfferCancel(params?: {}): Promise<implicitReturnType>;
    privatePostOfferNew(params?: {}): Promise<implicitReturnType>;
    privatePostOfferStatus(params?: {}): Promise<implicitReturnType>;
    privatePostOffers(params?: {}): Promise<implicitReturnType>;
    privatePostOffersHist(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancel(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelAll(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelMulti(params?: {}): Promise<implicitReturnType>;
    privatePostOrderCancelReplace(params?: {}): Promise<implicitReturnType>;
    privatePostOrderNew(params?: {}): Promise<implicitReturnType>;
    privatePostOrderNewMulti(params?: {}): Promise<implicitReturnType>;
    privatePostOrderStatus(params?: {}): Promise<implicitReturnType>;
    privatePostOrders(params?: {}): Promise<implicitReturnType>;
    privatePostOrdersHist(params?: {}): Promise<implicitReturnType>;
    privatePostPositionClaim(params?: {}): Promise<implicitReturnType>;
    privatePostPositionClose(params?: {}): Promise<implicitReturnType>;
    privatePostPositions(params?: {}): Promise<implicitReturnType>;
    privatePostSummary(params?: {}): Promise<implicitReturnType>;
    privatePostTakenFunds(params?: {}): Promise<implicitReturnType>;
    privatePostTotalTakenFunds(params?: {}): Promise<implicitReturnType>;
    privatePostTransfer(params?: {}): Promise<implicitReturnType>;
    privatePostUnusedTakenFunds(params?: {}): Promise<implicitReturnType>;
    privatePostWithdraw(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
