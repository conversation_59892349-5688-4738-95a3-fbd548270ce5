import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetMarketTickers(params?: {}): Promise<implicitReturnType>;
    publicGetMarketTicker(params?: {}): Promise<implicitReturnType>;
    publicGetMarketBooks(params?: {}): Promise<implicitReturnType>;
    publicGetMarketCandles(params?: {}): Promise<implicitReturnType>;
    publicGetMarketHistoryCandles(params?: {}): Promise<implicitReturnType>;
    publicGetMarketTrades(params?: {}): Promise<implicitReturnType>;
    publicGetMarketHistoryTrades(params?: {}): Promise<implicitReturnType>;
    publicGetMarketPlatform24Volume(params?: {}): Promise<implicitReturnType>;
    publicGetMarketOpenOracle(params?: {}): Promise<implicitReturnType>;
    publicGetMarketExchangeRate(params?: {}): Promise<implicitReturnType>;
    publicGetPublicInstruments(params?: {}): Promise<implicitReturnType>;
    publicGetPublicTime(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrder(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrdersPending(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrdersHistory(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrdersHistoryArchive(params?: {}): Promise<implicitReturnType>;
    privateGetTradeFills(params?: {}): Promise<implicitReturnType>;
    privateGetTradeFillsHistory(params?: {}): Promise<implicitReturnType>;
    privateGetTradeFillsArchive(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrderAlgo(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrdersAlgoPending(params?: {}): Promise<implicitReturnType>;
    privateGetTradeOrdersAlgoHistory(params?: {}): Promise<implicitReturnType>;
    privateGetOtcRfqTrade(params?: {}): Promise<implicitReturnType>;
    privateGetOtcRfqHistory(params?: {}): Promise<implicitReturnType>;
    privateGetAccountBalance(params?: {}): Promise<implicitReturnType>;
    privateGetAccountBills(params?: {}): Promise<implicitReturnType>;
    privateGetAccountBillsArchive(params?: {}): Promise<implicitReturnType>;
    privateGetAccountConfig(params?: {}): Promise<implicitReturnType>;
    privateGetAccountMaxSize(params?: {}): Promise<implicitReturnType>;
    privateGetAccountMaxAvailSize(params?: {}): Promise<implicitReturnType>;
    privateGetAccountTradeFee(params?: {}): Promise<implicitReturnType>;
    privateGetAccountMaxWithdrawal(params?: {}): Promise<implicitReturnType>;
    privateGetAssetCurrencies(params?: {}): Promise<implicitReturnType>;
    privateGetAssetBalances(params?: {}): Promise<implicitReturnType>;
    privateGetAssetAssetValuation(params?: {}): Promise<implicitReturnType>;
    privateGetAssetTransferState(params?: {}): Promise<implicitReturnType>;
    privateGetAssetBills(params?: {}): Promise<implicitReturnType>;
    privateGetAssetDepositLightning(params?: {}): Promise<implicitReturnType>;
    privateGetAssetDepositAddress(params?: {}): Promise<implicitReturnType>;
    privateGetAssetDepositHistory(params?: {}): Promise<implicitReturnType>;
    privateGetAssetWithdrawalHistory(params?: {}): Promise<implicitReturnType>;
    privateGetAssetDepositWithdrawStatus(params?: {}): Promise<implicitReturnType>;
    privateGetFiatDepositHistory(params?: {}): Promise<implicitReturnType>;
    privateGetFiatWithdrawHistory(params?: {}): Promise<implicitReturnType>;
    privateGetFiatChannel(params?: {}): Promise<implicitReturnType>;
    privateGetUsersSubaccountList(params?: {}): Promise<implicitReturnType>;
    privateGetUsersSubaccountApiKey(params?: {}): Promise<implicitReturnType>;
    privateGetAccountSubaccountBalances(params?: {}): Promise<implicitReturnType>;
    privateGetAssetSubaccountBalances(params?: {}): Promise<implicitReturnType>;
    privateGetAssetSubaccountBills(params?: {}): Promise<implicitReturnType>;
    privatePostTradeOrder(params?: {}): Promise<implicitReturnType>;
    privatePostTradeBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostTradeCancelOrder(params?: {}): Promise<implicitReturnType>;
    privatePostTradeCancelBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostTradeAmendOrder(params?: {}): Promise<implicitReturnType>;
    privatePostTradeAmendBatchOrders(params?: {}): Promise<implicitReturnType>;
    privatePostTradeOrderAlgo(params?: {}): Promise<implicitReturnType>;
    privatePostTradeCancelAlgos(params?: {}): Promise<implicitReturnType>;
    privatePostTradeCancelAdvanceAlgos(params?: {}): Promise<implicitReturnType>;
    privatePostOtcRfqQuote(params?: {}): Promise<implicitReturnType>;
    privatePostOtcRfqTrade(params?: {}): Promise<implicitReturnType>;
    privatePostAssetTransfer(params?: {}): Promise<implicitReturnType>;
    privatePostAssetWithdrawal(params?: {}): Promise<implicitReturnType>;
    privatePostAssetWithdrawalLightning(params?: {}): Promise<implicitReturnType>;
    privatePostAssetWithdrawalCancel(params?: {}): Promise<implicitReturnType>;
    privatePostFiatDeposit(params?: {}): Promise<implicitReturnType>;
    privatePostFiatCancelDeposit(params?: {}): Promise<implicitReturnType>;
    privatePostFiatWithdrawal(params?: {}): Promise<implicitReturnType>;
    privatePostFiatCancelWithdrawal(params?: {}): Promise<implicitReturnType>;
    privatePostAssetSubaccountTransfer(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
