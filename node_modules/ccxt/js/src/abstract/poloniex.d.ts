import { implicitReturnType } from '../base/types.js';
import { Exchange as _Exchange } from '../base/Exchange.js';
interface Exchange {
    publicGetMarkets(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbol(params?: {}): Promise<implicitReturnType>;
    publicGetCurrencies(params?: {}): Promise<implicitReturnType>;
    publicGetCurrenciesCurrency(params?: {}): Promise<implicitReturnType>;
    publicGetV2Currencies(params?: {}): Promise<implicitReturnType>;
    publicGetV2CurrenciesCurrency(params?: {}): Promise<implicitReturnType>;
    publicGetTimestamp(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsPrice(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolPrice(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolMarkPrice(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolMarkPriceComponents(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolOrderBook(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolCandles(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolTrades(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsTicker24h(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsSymbolTicker24h(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsCollateralInfo(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsCurrencyCollateralInfo(params?: {}): Promise<implicitReturnType>;
    publicGetMarketsBorrowRatesInfo(params?: {}): Promise<implicitReturnType>;
    privateGetAccounts(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsBalances(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsIdBalances(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsActivity(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsTransfer(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsTransferId(params?: {}): Promise<implicitReturnType>;
    privateGetFeeinfo(params?: {}): Promise<implicitReturnType>;
    privateGetAccountsInterestHistory(params?: {}): Promise<implicitReturnType>;
    privateGetSubaccounts(params?: {}): Promise<implicitReturnType>;
    privateGetSubaccountsBalances(params?: {}): Promise<implicitReturnType>;
    privateGetSubaccountsIdBalances(params?: {}): Promise<implicitReturnType>;
    privateGetSubaccountsTransfer(params?: {}): Promise<implicitReturnType>;
    privateGetSubaccountsTransferId(params?: {}): Promise<implicitReturnType>;
    privateGetWalletsAddresses(params?: {}): Promise<implicitReturnType>;
    privateGetWalletsAddressesCurrency(params?: {}): Promise<implicitReturnType>;
    privateGetWalletsActivity(params?: {}): Promise<implicitReturnType>;
    privateGetMarginAccountMargin(params?: {}): Promise<implicitReturnType>;
    privateGetMarginBorrowStatus(params?: {}): Promise<implicitReturnType>;
    privateGetMarginMaxSize(params?: {}): Promise<implicitReturnType>;
    privateGetOrders(params?: {}): Promise<implicitReturnType>;
    privateGetOrdersId(params?: {}): Promise<implicitReturnType>;
    privateGetOrdersKillSwitchStatus(params?: {}): Promise<implicitReturnType>;
    privateGetSmartorders(params?: {}): Promise<implicitReturnType>;
    privateGetSmartordersId(params?: {}): Promise<implicitReturnType>;
    privateGetOrdersHistory(params?: {}): Promise<implicitReturnType>;
    privateGetSmartordersHistory(params?: {}): Promise<implicitReturnType>;
    privateGetTrades(params?: {}): Promise<implicitReturnType>;
    privateGetOrdersIdTrades(params?: {}): Promise<implicitReturnType>;
    privatePostAccountsTransfer(params?: {}): Promise<implicitReturnType>;
    privatePostSubaccountsTransfer(params?: {}): Promise<implicitReturnType>;
    privatePostWalletsAddress(params?: {}): Promise<implicitReturnType>;
    privatePostWalletsWithdraw(params?: {}): Promise<implicitReturnType>;
    privatePostV2WalletsWithdraw(params?: {}): Promise<implicitReturnType>;
    privatePostOrders(params?: {}): Promise<implicitReturnType>;
    privatePostOrdersBatch(params?: {}): Promise<implicitReturnType>;
    privatePostOrdersKillSwitch(params?: {}): Promise<implicitReturnType>;
    privatePostSmartorders(params?: {}): Promise<implicitReturnType>;
    privateDeleteOrdersId(params?: {}): Promise<implicitReturnType>;
    privateDeleteOrdersCancelByIds(params?: {}): Promise<implicitReturnType>;
    privateDeleteOrders(params?: {}): Promise<implicitReturnType>;
    privateDeleteSmartordersId(params?: {}): Promise<implicitReturnType>;
    privateDeleteSmartordersCancelByIds(params?: {}): Promise<implicitReturnType>;
    privateDeleteSmartorders(params?: {}): Promise<implicitReturnType>;
    privatePutOrdersId(params?: {}): Promise<implicitReturnType>;
    privatePutSmartordersId(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketAllInstruments(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketInstruments(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketOrderBook(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketCandles(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketIndexPriceCandlesticks(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketPremiumIndexCandlesticks(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketMarkPriceCandlesticks(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketTrades(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketLiquidationOrder(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketTickers(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketMarkPrice(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketIndexPrice(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketIndexPriceComponents(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketFundingRate(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketOpenInterest(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketInsurance(params?: {}): Promise<implicitReturnType>;
    swapPublicGetV3MarketRiskLimit(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3AccountBalance(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3AccountBills(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3TradeOrderOpens(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3TradeOrderTrades(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3TradeOrderHistory(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3TradePositionOpens(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3TradePositionHistory(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3PositionLeverages(params?: {}): Promise<implicitReturnType>;
    swapPrivateGetV3PositionMode(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3TradeOrder(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3TradeOrders(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3TradePosition(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3TradePositionAll(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3PositionLeverage(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3PositionMode(params?: {}): Promise<implicitReturnType>;
    swapPrivatePostV3TradePositionMargin(params?: {}): Promise<implicitReturnType>;
    swapPrivateDeleteV3TradeOrder(params?: {}): Promise<implicitReturnType>;
    swapPrivateDeleteV3TradeBatchOrders(params?: {}): Promise<implicitReturnType>;
    swapPrivateDeleteV3TradeAllOrders(params?: {}): Promise<implicitReturnType>;
}
declare abstract class Exchange extends _Exchange {
}
export default Exchange;
