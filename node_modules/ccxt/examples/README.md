# CCXT Cryptocurrency Trading Library Examples

![preview](https://user-images.githubusercontent.com/1294454/31798504-ca2af4cc-b53c-11e7-946c-620d744f6d16.gif)

To run the ccxt examples from any folder type one of the following commands in console.

## Typescript

[Typescript Examples](https://github.com/ccxt/ccxt/tree/master/examples/ts/)

---

## JavaScript

[JavaScript Examples](https://github.com/ccxt/ccxt/tree/master/examples/js/)

```shell
node path/to/example.js # substitute for actual filename here
```

These examples might require the following super-useful high-quality Node.js modules by [xpl](https://github.com/xpl):

- [ololog](https://github.com/xpl/ololog): Platform-agnostic logging with blackjack and hookers ([ololog @ npm](https://npmjs.com/package/ololog))
- [ansicolor](https://github.com/xpl/ansicolor): A quality JavaScript library for the ANSI color/style management ([ansicolor @ npm](https://npmjs.com/package/ansicolor))
- [as-table](https://github.com/xpl/as-table): A simple function that prints objects as ASCII tables ([as-table @ npm](https://npmjs.com/package/as-table))

All of the modules above are installed with the ccxt library devDependencies by npm automatically.

<img width="842" alt="bitcoin ascii chart" src="https://user-images.githubusercontent.com/1294454/29673849-156f58c6-88f9-11e7-8955-fb3f37467234.png">

---

## PHP

[PHP Examples](https://github.com/ccxt/ccxt/tree/master/examples/php/)

```shell
php -f path/to/example.php # substitute for actual filename here
```

---

## Python

[Python Examples](https://github.com/ccxt/ccxt/tree/master/examples/py/)

![basic-chart](https://user-images.githubusercontent.com/1294454/29979754-6d62354c-8f4f-11e7-9e0a-22e87b4a093b.jpg)

```shell
python path/to/example.py # substitute for actual filename here
```

-------------------------------------------------------------------------------

## See Also

This section includes links to friendly projects, tutorials, articles, videos, courses and other materials related to CCXT.

[Freqtrade](https://www.freqtrade.io) – leading opensource cryptocurrency algorithmic trading software based on CCXT!

[Blockpit](https://www.blockpit.io) – Tax reporting and portfolio tracking for Cryptocurrencies, Cryptoderivates and DeFi.

[DCAStack](https://www.dcastack.com/) – An automated open-source Dollar Cost Averaging bot for crypto (built with CCXT).

[Projects based on ccxt](https://github.com/ccxt/ccxt/network/dependents) – A list of hundreds of ccxt-based projects by developers from all over the world!

[The Evolution Of CCXT](https://www.youtube.com/watch?v=KnyduYTFm1c) – A Gource visualization on the development of the CCXT code repository.

[Playing with CCXT in Google Colab](https://medium.com/@ccxt/playing-with-ccxt-in-google-colab-23522ac8a6cb) – An article on how useful Colab can be for quick prototyping and testing your trading ideas with CCXT.

[Ultimate Guide to Mastering Cryptocurrency Trading Orders with Python](https://robottraders.io/blog/guide-ccxt-orders) – A great tutorial on trading orders with CCXT by Robot Traders.

[Mastering Cryptocurrency Trading: From Data to Strategy with Python](https://profitview.net/blog/mastering-cryptocurrency-trading-from-data-to-strategy-with-python) – An in-depth article guiding through trading strategy development with CCXT and Python.

[What is the Best Crypto Trading Bot in 2020?](https://www.hodlbot.io/blog/ultimate-guide-on-crypto-trading-bots) – Ultimate guide on crypto trading bots in 2020 by HodlBlog.

[Enigma Catalyst](https://blog.enigma.co/enigma-announces-catalyst-0-4-our-biggest-release-yet-fa31a5ffa4b1) – The major effort towards decentralized exchanges integrates ccxt!

[Geographic Latency in Crypto: How to Optimally Co-Locate Your AWS Trading Server to Any Exchange API](https://elitwilliams.medium.com/geographic-latency-in-crypto-how-to-optimally-co-locate-your-aws-trading-server-to-any-exchange-58965ea173a8) – An article overviewing the aspects of latencies for global cryptocurrency trading.

[CCXT - Cryptocurrency Exchange Trading Library Tutorial](https://www.youtube.com/watch?v=2Zdm2ISdm1Q) – A YouTube video tutorial on CCXT!

[CCXT Crypto Bot - Order Execution with Python](https://www.youtube.com/watch?v=1PEyddA1y5E) – Making a CCXT Bot on YouTube!

[FTX API (Crypto Exchange) and Google Cloud Functions](https://www.youtube.com/watch?v=mtpA3vDg5js) – A video on trading with CCXT and Google Cloud Functions!

[VectorBT Scheduler, Alpaca Crypto API, Stochastic Oscillator](https://www.youtube.com/watch?v=ZffdACbvFjc) – A video tutorial on CCXT + VectorBT Scheduler + Alpaca Crypto API + Stochastic Oscillator.

[Coinbase Pro, TradingView Webhooks, and Google Cloud Functions](https://www.youtube.com/watch?v=3jA2vAJSdAI) – How to automate your trading by connecting Coinbase Pro to Google Cloud Functions using TradingView Webhooks and CCXT!

[CCXT - Crypto Trading](https://www.youtube.com/playlist?list=PLvzuUVysUFOv5XEPHeqefgyHZcDfLxH-W) – An awesome video playlist on CCXT and tons of other cool videos by [Part Time Larry](https://www.youtube.com/c/parttimelarry)!

[CCXT Python Crypto Tutorials](https://www.youtube.com/playlist?list=PLXrNVMjRZUJg4OV2qfmFMpI5k3FRrB0NE) – CCXT Python Crypto Tutorials on YouTube!

[Python and CCXT Grid Bot Tutorial](https://www.youtube.com/watch?v=QzqMGX4Qk1A) – A video tutorial on building a Grid Bot with Python and CCXT!

[JavaScript CCXT Grid Bot vs. Python](https://www.youtube.com/watch?v=kLATKNzbUOE) – A video comparison of using CCXT to write a Grid Bot in JavaScript vs Python.

[Crypto Trading Bot In Python](https://medium.com/coinmonks/crypto-trading-bot-on-python-22a245f1a9d) – An article on structuring a basic bot with CCXT and Python.

[Writing a crypto trading bot](https://www.kleemans.ch/writing-a-crypto-trading-bot) – How to write an automated trading bot, for fun and profit.

[Daily Binance Crypto Trade Signals](https://medium.com/coinmonks/daily-binance-us-crypto-trade-signals-fda4e8a205c8) – How to use Kaggle’s schedule feature to build a daily cryptocurrency trade signals webpage.

[graphql-ccxt](https://github.com/fibo/graphql-ccxt) – Joins together GraphQL and CCXT: can fetch prices, read balance, open orders, ... on multiple exchanges at once.

[ccxt-go](https://github.com/prompt-cash/ccxt-go) – CCXT port to Go (Golang).

[How to use Chat GPT & CCXT to Algo Trade](https://www.youtube.com/watch?v=FVS-_bMZQAQ) – A YouTube video on pairing ChatGPT with CCXT for algorithmic trading.

[The GDAX Trading Toolkit (GTT)](https://github.com/coinbase/gdax-tt) – a trading toolkit developed by the GDAX team that integrates the CCXT library!

[CC Power Analytics Part 1: How to get the data of the exchanges](https://www.linkedin.com/pulse/part-1-cc-power-analytics-how-get-data-exchanges-steve-rein/) – The first part of a series of articles on cryptocurrency analytics.

[Looking for arbitrage opportunies with ccxt](https://steemit.com/steemdev/@codewithcheese/looking-for-arbitrage-opportunies-with-javascript-library-cctx-supporting-70-exchanges) – An article @steemit on getting arbitrage started with ccxt for crypto-arbitrage.

[Use CCXT to calculate Cryptocurrency trade indicators](https://itnext.io/use-ccxt-to-calculate-cryptocurrency-trade-indicators-102a3ac1428e) – an article @ Medium on building basic indicators using CCXT.

[A n00bs Guide To Deep Cryptocurrency Trading](https://medium.com/@LeonFedden/deep-cryptocurrency-trading-1e64af6d280a) – An article @ Medium on deep neural trading using ccxt for backend tasks.

[A Video Crash-Course On Using CCXT](https://www.youtube.com/playlist?list=PLIFBTFgFpoJ-xGRz3v_2nF7f9HKZrfSpj) – A series of awesome video tutorials on getting started with CCXT!

[Integrando el API de Binance y Kucoin con CCXT en Python](https://juancisneros.com.ve/integrando-api-binance-kucoin-ccxt-python/) – An article in Spanish on using CCXT in Python.

[Let’s write a cryptocurrency bot](https://medium.com/@joeldg/an-advanced-tutorial-a-new-crypto-currency-trading-bot-boilerplate-framework-e777733607ae) – A multi-part advanced tutorial on a new cryptocurrency trading framework also integrating ccxt.

[Naas.ai CCXT Templates](https://www.naas.ai/tools/ccxt) – Naas notebooks enable you to easily access data, automation and AI.

[How to Buy and Sell Bitcoin with Python](https://python.plainenglish.io/how-to-buy-and-sell-bitcoin-with-python-f83d893c393) – How to Buy and Sell Bitcoin with Python using CCXT.

[Beginners Python Guide To CCXT Crypto API](https://wire.insiderfinance.io/beginners-python-guide-to-ccxt-crypto-api-no-api-key-required-4dc928f3af94) – A guide to working with CCXT in Python.

[Download Cryptocurrency Data with CCXT](https://backtest-rookies.com/2018/03/08/download-cryptocurrency-data-with-ccxt/) – A sample script to download historical data from exchanges with a video overview: [Easy Python script to download crypto currency market data with CCXT package](https://www.youtube.com/watch?v=PTGkJsrF7kQ).

[How to Properly Download and Validate Cryptocurrency Data](https://medium.com/@dmitriy.kavyazin/how-to-properly-download-and-validate-cryptocurrency-data-fa90ac1c5f8b) – An article explaining how to properly obtain cryptocurrency price and volume data for use in trading and statistical modeling (with CCXT).

[Step-By-Step Guide to Installing Python and CCXT Library](https://www.youtube.com/watch?v=bmKAtBfGk50) – A video tutorial on installing CCXT and Python on MS Windows.

[ccxt 를 사용하여 가상화폐 데이터 다운로드하기](https://antilibrary.org/2174) – A Korean translation of the above article.

[https://www.youtube.com/watch?v=3PTU8tTMcXc](How to get the crypto historical data with ccxt, python) – A video @ YouTube in Korean on accessing crypto data with CCXT in Python.

[Getting started with CCXT Crypto Exchange Library and Python
](https://medium.com/@pknerd/getting-started-with-ccxt-crypto-exchange-library-and-python-93175d5a898d) – An article on using CCXT to pull different kinds of data from exchanges and for trading automation.

[Experiments : Creating a Crypto Twitter Bot to Announce Newly Listed CryptoCoins](https://medium.com/@kennychuaio/experiments-creating-a-crypto-twitter-bot-to-announce-newly-listed-cryptocoins-9cd23930f5cb) – An article on how to integrate with CCXT and Twitter to get new currencies from exchanges automatically.

[CCXT Exercise Index (Python)](https://coil.com/p/sarantium/CCXT-Exercise-Index/U3-ljsKK1) – All CCXT exercises for Python by number and subject. It will be continuously updated as more exercises are added!

[How to make your own trading bot](https://codeburst.io/how-to-make-your-own-trading-bot-83b5c6e35036) – A tutorial on algortrading in Python.

[Comprehensive Cryptocurrency Trading Utility with ccxt: Real-time and Historical Data Analysis](https://medium.com/@deepml1818/comprehensive-cryptocurrency-trading-utility-with-ccxt-real-time-and-historical-data-analysis-6b9cbba57375) – A tutorial on technical analysis with CCXT.

[Writing crypto trading bot in Python with Telegram and ccxt](https://medium.com/@maxAvdyushkin/writing-crypto-trading-bot-in-python-with-telegram-and-ccxt-80632a00c637) – A tutorial on programming a Telegram bot in Python with CCXT

[Data science is eating the world — Here’s how you can start exploring it](https://medium.com/covee-network/data-science-is-eating-the-world-heres-how-you-can-start-exploring-it-37501414af15) – An article on collecting and analyzing financial big data.

[Chasing fake volume: a crypto-plague](https://medium.com/@sylvainartplayribes/chasing-fake-volume-a-crypto-plague-ea1a3c1e0b5e) – An article @ Medium on trading volumes analysis.

[TradingBot series — Architecture for a trading bot](https://medium.com/@MattGosden/tradingbot-series-architecture-for-a-trading-bot-ac2352508c82) – An article on building a trading bot using Python, exchange APIs, Backtrader, Telegram and CCXT.

[Create Your Own Python Server to Gather High Granularity Market Data for Cryptocurrencies (And use it to create a Neural Network Trading Bot)](https://blog.goodaudience.com/create-your-own-python-server-to-gather-high-granularity-market-data-for-cryptocurrencies-and-use-8a4b2f46e04c) – A read on collecting and analyzing data with neural neutworks and CCXT.

[How to build an AI crypto robo trader on binance in python with jupyter notebook](https://medium.com/@dumaysacha/how-to-build-an-ai-crypto-robo-trader-on-binance-in-python-with-jupyter-notebook-episode-1-97f26a805722) – An article on creating algorithhmic trading bots with Jupyter Notebooks and CCXT.

[Advanced Cryptocurrency Trading Bot — Python — Open Source](https://medium.com/@BlockchainEng/advanced-cryptocurrency-trading-bot-python-open-source-chapters-5-6-7-72b36b378750) – An article in the course [Creating a Cryptocurrency Trading Bot in Python](https://medium.com/@BlockchainEng/how-to-build-an-automated-cryptocurrency-trading-bot-on-binance-with-python-2cd9b5874e44) by [@BlockchainEngineer](https://medium.com/@BlockchainEng)

[Use of JavaScript for Blockchain Projects](https://medium.com/@WebbyLab/use-of-javascript-for-blockchain-projects-104191b9bb0e) – An overview of the available tools for developers.

[Crypto-Aggregator: My Open Source Alternative to CoinMarketCap](https://www.hackernoon.com/crypto-aggregator-an-open-source-alternative-for-coinmarketcap-3a39d31wt) – An article on how to make your own aggregator similar to CoinMarketCap with Node.js and CCXT.

[Building some node js tools to setup machine learning trading](https://medium.com/@kid.bytes/building-some-node-js-tools-to-setup-machine-learning-trading-pt-2-13f44e1b1df7) – An article in a series on setting up some tools to get started with machine learning for crypto trading.

[Trade and Invest Smarter — The Reinforcement Learning Way](https://towardsdatascience.com/trade-smarter-w-reinforcement-learning-a5e91163f315) – A deep dive into  trading and investing using deep reinforcement learning with Python.

[Cryptocurrencies and APIs](https://jonc.dev/cryptocurrency) – A presentation given to kick off the Bloomberg cryptocurrency hackathon and as a Recurse Center talk.

[How to use CCXT to algo trade](https://www.youtube.com/watch?v=3EQsDdjZTlc) – An easy to watch video tutorial on how to use CCXT for algotrading.

[Perform Currency Conversions And Get Bitcoin Prices In Python](http://www.maxpython.com/packages/perform-currency-conversions-and-get-bitcoin-prices-in-python.php) – an article showing how to use CCXT to get exchange rates, Bitcoin prices and perform currency conversions.

[Using Apache Airflow ETL to fetch and analyze BTC data](https://itnext.io/using-apache-airflow-etl-to-fetch-and-analyze-btc-data-1adab96c410c) – how to use CCXT with Apache Airflow ETL for Bitcoin pricing data analysis.

[Generic Position and Pnl Calculation](https://norman-lm-fung.medium.com/generic-position-and-pnl-calculation-67de9a9829be) – Using CCXT for fetching positions to calculate PnL.

[TensorTrade: Trade Efficiently with Reinforcement Learning](https://github.com/tensortrade-org/tensortrade) – TensorTrade is an open source Python framework for building, training, evaluating, and deploying robust trading algorithms using reinforcement learning.

[Never Pay Fees Again: Mimic Market Buys with Limit Chase Orders in Python](https://nukewhales.com/p/limitchase.html) – A tutorial on how to automate your maker fees.

[Step-By-Step Guide to Installing Python and CCXT Library](https://www.youtube.com/watch?v=bmKAtBfGk50) – A step-by-step video guide to installing Python and CCXT library which are used by many tools and bots running on MT4 and MT5 platforms on Windows.

[Download Historical Data of All Cryptocoins with CCXT for free](https://www.linkedin.com/pulse/download-historical-data-all-cryptocoins-ccxt-gursel-karacor/) – An article on linked.in explaining how to download historical market data with CCXT.

[Automated Trading Bots for Profit](https://python.plainenglish.io/automated-trading-bots-for-profit-659da17a4715) – Automating trading bots with CCXT and Python.

[Avoiding Bear Traps in Options Trading: How to Safeguard Your Investments with a Python Bot](https://medium.com/coinmonks/avoiding-bear-traps-in-options-trading-how-to-safeguard-your-investments-with-a-python-bot-6289eecb3048) – An article by Coinmonks on risk management with options trading using CCXT and Python.

[Building a Trading Bot in Python : Step-by-Step Guide with Examples](https://medium.com/@kritjunsree/building-a-trading-bot-in-python-a-step-by-step-guide-with-examples-6898244016cd) – An article @ Medium explaining automated trading strategies in Python.

[ByBit API Tutorial with CCXT: Functions Explained](https://medium.com/@yussf_26476/bybit-api-tutorial-with-ccxt-functions-explained-a7e1b4579b4a) – An article @ Medium explaining trading functions from CCXT for Bybit.

[How to work with CCXT in Python](https://zetcode.com/python/ccxt/) – An article showing how to work with cryptocurrency exchange library (CCXT) in Python.

[CryptoCurrency eXchange Trading library - CCXT](https://barbotine.medium.com/crypto-currency-exchange-trading-library-ccxt-34ed66ebede8) – An overview of CCXT.

[Simplifying Trading Automation with CCXT](https://j4nt4ncrypto.medium.com/simplifying-trading-automation-with-ccxt-creating-orders-and-fetching-positions-on-kucoin-futures-d42c329577de) – Creating Orders and Fetching Positions on Kucoin Futures.

[https://python.plainenglish.io/cryptocurrency-trading-with-python-5f655d06a4bd](Cryptocurrency Trading with Python) – How to trade cryptocurrencies with CCXT in Python.

[비트코인 해외 거래소 API - 바이낸스 CCXT설치](https://www.youtube.com/watch?v=7dMGbjwZwlU) – Using CCXT to trade with Binance API, a video overview of CCXT in Korean.

[ccxt_라이브러리](https://www.youtube.com/watch?v=ifkJ5FtMpKY) – A quick short demo of CCXT on YouTube.

https://www.youtube.com/watch?v=K-EZ9QXezZM

[CCXTが便利な理由！CCXTライブラリをインストールしよう](https://ryota-trade.com/?p=476) – An article in Japanese on getting CCXT up and running.

[如何通過CCXT下載比特幣歷史數據? How to download Bitcoin price data with CCXT](https://www.youtube.com/watch?v=Nozqu7WyCDw) – A video tutorial on how to download historical Bitcoin price data using CCXT.

[暗号通貨自動売買 RSI CCXTとpythonで自動売買 プログラミング bitget](https://www.youtube.com/watch?v=BAqnx-XkA_8) – Automatic cryptocurrency trading with CCXT and Python

[Bitflyerや各取引所の個別APIをCCXTライブラリ経由で直接利用する方法](https://ryota-trade.com/?p=629) – How to use Bitflyer and individual API of each exchange directly via the CCXT library.

[CCXTライブラリでBitflyerに注文を出す方法をマスターする](https://ryota-trade.com/?p=662) – Master placing orders on Bitflyer with the CCXT library.

[CCXTでBitflyerに出した注文を管理・キャンセルする方法](https://ryota-trade.com/?p=759) – How to manage and cancel orders placed on Bitflyer with CCXT

[Bitflyerの未約定の全注文をCCXTで一括でキャンセルする方法](https://ryota-trade.com/?p=833) – How to cancel all uncommitted orders of Bitflyer in bulk with CCXT

[Python3とCCXTライブラリを用いたBitMEX自動売買bot作成Tips](https://note.mu/akagami/n/n0af0a96c261f) – An article in Japanese on getting started with CCXT and a few bot tips.

[ccxtの使い方:pythonで価格取得や残高確認できるAPI ccxt](https://oretano.com/ccxt-resume) – An nice overview of using CCXT in Python.

[CCXT 开发手册](https://www.chainside.info/ccxt-dev-book/ccxt-01-%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8C%E7%AE%80%E4%BB%8B/) – CCXT Manual translated to Chinese.

[交易所接口开发 使用CCXT连接BINANCE交易所获取行情 StudyQuant](https://www.youtube.com/watch?v=SlHBhE_Kz0Y) – A video tutorial on CCXT in Chinese (part 1).

[交易所接口开发 2使用CCXT连接BINANCE交易所获取订单余额 StudyQuant](https://www.youtube.com/watch?v=K-EZ9QXezZM) – A video tutorial on CCXT in Chinese (part 2).

[如何学习CCXT框架，CCXT框架的使用以及修改交易所的API设置。 讲解如何学习CCXT框架，如何修改和设置CCXT框架中交易所实例的内容设置， 如何获取Tick和行情数据等。](https://www.youtube.com/watch?v=sj8g3gz0k24) – A video on getting started with CCXT and working with market data.

[第二十课：如何使用CCXT框架获取交易所账户信息，下单和查询订单等私有API接口的调用， 以及如何处理交易所返回的数据](https://www.youtube.com/watch?v=GF0uVVIMbn8) – A video on working with private data in CCXT.

[第21课：如何开微信交易所下单机器人以及如何修改CCXT火币交易所api设置等问题解答](https://www.youtube.com/watch?v=ezeOX3-ZzUc) – A video explaining how to write a WeChat bot and how to configure CCXT.

[Python数字货币量化交易开发入门视频-利用CCXT获取bitmex交易所的行情数据](https://www.bilibili.com/video/av44339579/) – A video in Chinese on how to get started with CCXT in Python and a tutorial on OHLCV + Pandas export to CSV.

[BitMEX自動売買Bot「MANA 1.0.1」の全て](https://note.mu/moycoin/n/n6f585ea854d4) – An example of automatic BitMEX Bot MANA 1.0.1.

[BitMEX 自動売買BOT開発 (API編) ① 開発環境の構築、APIライブラリの詳細、全APIマップ](https://note.mu/mazmex7/n/n1a3a0293ce82) – An article on developing an automatic BitMEX trading bot.

[仮想通貨トレード Bot 制作に便利な CCXT ライブラリに関する知見まとめ (Python 随時更新)](http://www.stray-scrapbook.work/entry/2018/04/03/205700) – A summary on Python version of CCXT API in Japanese.

[Python3とCCXTを使用して仮想通貨の自動売買プログラムを作る](http://www.hacky.xyz/entry/2018/03/18/200822) – Automatic cryptocurrency trading using Python 3 and CCXT

[ccxtを使って裁定取引botを作ってみたらなぜか虚しくなった件](https://qiita.com/reon777/items/21ed87f19cdd50f08bd9) – An article in Japanese explaining the basics of programming an arbitrage bot with CCXT.

[Python 3 / BitMEX の BOT を作ろう CCXT + BOT サンプルコード 〈基礎編〉](https://note.mu/mman/n/n5a9083864335) – A sample of basic BitMEX bot with CCXT in Python 3.

[ccxtがbtcfxbot界隈でちょっと話題になっていたので使ってみた](http://cryptojapan.ml/entry/2018/03/01/151752) – Trying CCXT for a basic bot.

[python異步加協程獲取比特幣市場信息](https://hk.saowen.com/a/18a648f24d6e7f54981e9db4411b56730a35dd2b3b27519083543bcd6198cd27) – An article in Chinese on using CCXT with Python.

[数字货币量化交易1 【群友 Shadow 自制】ccxt 的python版本安装及使用入门](https://www.bilibili.com/video/av21795165) – Trading Digital Currencies 1: Installing and using the Python version of ccxt (video in Chinese)

[数字货币量化交易2 【群友 林军 自制】ccxt Unified API命令详解及node版本使用演示](https://www.bilibili.com/video/av21842290) – Trading Digital Currencies 2: Using unified CCXT API (video in Chinese)

[数字货币量化交易3 ccxt Custom API命令详解及node版本使用演示](https://www.bilibili.com/video/av21842988)
– Trading Digital Currencies 3: Details on using custom exchange-specific [implicit methods](https://github.com/ccxt/ccxt/wiki/Manual#implicit-api-methods) in ccxt (video in Chinese)

[币圈程序化交易被使用最多的开源项目：ccxt](https://bihu.com/article/1629320804) – An article explaining what CCXT is.

[CCXT框架爬取Bybit交易所历史行情数据和pandas数据处理](https://www.youtube.com/watch?v=iNm11U7icIs) – A video explaining how to use CCXT with Pandas in Chinese.

[Algorithmic Trading - Aula 4](https://www.youtube.com/watch?v=BkKebXrhMGY) – A video introduction to algorithmic trading with CCXT from Curso Algo Trading in Portuguese language.

[Лучшая криптотрейдинг библиотека?](http://medium.com/@vladthelittleone/лучшая-криптотрейдинг-библиотека-67e308f96c1f) – An article in Russian on setting up CCXT to connect and trade with crypto exchanges.

[Как использовать CCXT и Python для работы с криптовалютами](https://sky.pro/media/kak-ispolzovat-python-dlya-raboty-s-kriptovalyutami/) – An article in Russian explaining how to trade cryptocurrencies using CCXT in Python.

[Open source бот для торгов на Binance](https://habr.com/ru/articles/737236/) – A russian article on building an open source trading bot with CCXT.

[Présentation de projet - Créer un Bot Telegram de A à Z (+CCXT)](https://www.youtube.com/watch?v=yayJKRtg5M4) – La création d'un bot Telegram en Node.js, qui va rechercher des informations sur les différentes marketpalce de crypto-monnaies, et cela via la librairie CCXT.

[ربات آربیتراژ با CCXT و پایتون - آموزش ساخت ربات آربیتراژ ارز دیجیتال - ویژه مبتدیان](https://www.youtube.com/watch?v=GmIrL0btU-4) – A CCXT Tutorial On Quick Arbs In Persian language

[برمجة روبوت تداول الجزء الاول "ccxt"](https://www.youtube.com/watch?v=8y41lRidGzY) – A video tutorial on trading bot programming with CCXT in Arabic, part 1.

["ccxt" تداول ألي الجزء الثاني](https://www.youtube.com/watch?v=vcAom-RS-6U) – A video tutorial on trading bot programming with CCXT in Arabic, part 2.

[Simple trading bot in JavaScript using ~40 lines of code](https://www.tuicool.com/articles/vqymAj3) – A demo of how to make a trading bot with CCXT.

[CCXT Python: Exploring Cryptocurrency Trading on Binance, Kucoin, and Bitmex](https://awstip.com/ccxt-python-exploring-cryptocurrency-trading-on-binance-kucoin-and-bitmex-b2bae06ce79c) – An article @ Medium on programming a trading script for Binance, Kucoin, and BitMEX.

[Using ccxt and technicalindicators to calculate MACD for BTC/USDT with Node.js](https://runkit.com/dhilipb/macd-for-btc-usdt) – A runkit sample showing how to fetch data and run statistical calculations.

[Which Bitcoin crypto currency bot project? Gekko vs ccxt vs Tribeca vs Blackbird](https://www.youtube.com/watch?v=Bn2p-nkbVdE) – A video comparison of opensource cryptocurrency trading platforms.

[LOC-Extension](https://github.com/walkjivefly/LOC-Extension) – a LibreOffice extension which embeds ccxt to provide cryptocurrency price lookup in your spreadsheets.

[CryptoMon Bot](https://github.com/jchristov/cryptomon-bot) - helps tracking of your cryptocurrency investments and making smart, informed buy/sell decisions.

[ZenBot](https://github.com/carlos8f/zenbot) - a command-line cryptocurrency trading bot using Node.js and MongoDB.

[bitcoin-chart-cli](https://github.com/madnight/bitcoin-chart-cli) by [madnight](https://github.com/madnight) – a command-line console util that draws Bitcoin, Ether, Litecoin and many altcoin charts right in the terminal!

![bitcoin-chart-cli](https://camo.githubusercontent.com/494806efd925c4cd56d8370c1d4e8b751812030a/68747470733a2f2f692e696d6775722e636f6d2f635474467879362e706e67)

It uses the [asciichart](https://github.com/kroitor/asciichart) JavaScript module by [kroitor](https://github.com/kroitor) for nice-looking lightweight ASCII line charting )) Both packages are available in npm!

![asciichart](https://cloud.githubusercontent.com/assets/1294454/22818709/9f14e1c2-ef7f-11e6-978f-34b5b595fb63.png)

Special thx to [MitchTalmadge](https://github.com/MitchTalmadge) for porting this package to Java language! Java-people, check it out here: [ASCIIGraph](https://github.com/MitchTalmadge/ASCIIGraph).



