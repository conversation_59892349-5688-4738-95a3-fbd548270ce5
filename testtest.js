const ccxt = require('ccxt');

async function checkFundingRatesSupport() {
    console.log('Биржи с поддержкой метода fetchFundingRates:\n');

    // Получаем список всех поддерживаемых бирж
    const exchanges = ccxt.exchanges;
    let supportedCount = 0;

    for (const exchangeId of exchanges) {
        try {
            // Создаем экземпляр биржи
            const ExchangeClass = ccxt[exchangeId];
            const exchange = new ExchangeClass();

            // Проверяем поддержку метода fetchFundingRates
            const supportsFundingRates = exchange.has['fetchFundingRates'] || false;

            // Выводим только биржи с поддержкой fetchFundingRates
            if (supportsFundingRates) {
                console.log(exchangeId);
                supportedCount++;
            }

        } catch (error) {
            // Пропускаем биржи с ошибками создания экземпляра
        }
    }

    console.log(`\nВсего найдено бирж с поддержкой fetchFundingRates: ${supportedCount}`);
}

// Запускаем проверку 
checkFundingRatesSupport().catch(console.error);
